{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 2040997289075261528, "path": 9132151502926973543, "deps": [[1573238666360410412, "rand_chacha", false, 3763785689652148976], [2924422107542798392, "libc", false, 921958181130198961], [18130209639506977569, "rand_core", false, 13850071097890013695]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-ios/release/.fingerprint/rand-225120814d7fe8c2/dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 3694122122034060669}