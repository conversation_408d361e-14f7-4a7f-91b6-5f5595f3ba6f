{"rustc": 15497389221046826682, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2040997289075261528, "path": 307337821104044350, "deps": [[555019317135488525, "regex_automata", false, 7222847715819420756], [2779309023524819297, "aho_corasick", false, 9423409508134693315], [3129130049864710036, "memchr", false, 12354038300774068531], [9408802513701742484, "regex_syntax", false, 781136803036793801]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-ios/release/.fingerprint/regex-d8e80da389428ff2/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 3694122122034060669}