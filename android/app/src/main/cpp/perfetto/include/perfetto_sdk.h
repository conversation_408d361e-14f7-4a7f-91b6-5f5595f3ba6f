#ifndef PERFETTO_SDK_H
#define PERFETTO_SDK_H

#include <android/trace.h>
#include <string>

// Real Perfetto-compatible macros using Android Trace API
#define TRACE_EVENT_BEGIN(category, name) \
    ATrace_beginSection(name)

#define TRACE_EVENT_END() \
    ATrace_endSection()

#define TRACE_EVENT_INSTANT(category, name) \
    do { ATrace_beginSection(name); ATrace_endSection(); } while(0)

#define TRACE_COUNTER(category, name, value) \
    ATrace_setCounter(name, value)

// Async events (Android 10+)
#if __ANDROID_API__ >= 29
#define TRACE_EVENT_ASYNC_BEGIN(category, name, id) \
    ATrace_beginAsyncSection(name, id)

#define TRACE_EVENT_ASYNC_END(category, name, id) \
    ATrace_endAsyncSection(name, id)
#else
#define TRACE_EVENT_ASYNC_BEGIN(category, name, id) \
    ATrace_beginSection(name)

#define TRACE_EVENT_ASYNC_END(category, name, id) \
    ATrace_endSection()
#endif

#endif // PERFETTO_SDK_H