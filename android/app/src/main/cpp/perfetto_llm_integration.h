#ifndef PERFETTO_LLM_INTEGRATION_H
#define PERFETTO_LLM_INTEGRATION_H

#include <string>
#include <atomic>
#include <chrono>
#include <thread>
#include <memory>

class PerfettoLLMTracer {
public:
    static PerfettoLLMTracer& getInstance();
    
    // Core tracing operations
    bool initialize();
    bool startTrace(const std::string& categories);
    bool stopTrace();
    bool isTracing() const;
    
    // LLM-specific trace events (Option 1 & 2 compatible)
    void traceInferenceStart(const std::string& model_name, int context_length);
    void traceInferenceEnd();
    void tracePrefillStart(int token_count);
    void tracePrefillEnd();
    void traceDecodeStart();
    void traceDecodeEnd();
    void traceTokenGenerated(const std::string& token, int token_id);
    
    // System metrics (for academic analysis)
    void recordCpuUsage(double cpu_percent);
    void recordMemoryUsage(long memory_bytes);
    void recordSystemMetrics();

    // Enhanced monitoring methods
    double getCurrentCpuUsage();
    double getProcessCpuUsage();
    long getCurrentMemoryUsage();
    long getProcessMemoryUsage();
    long getProcessVmSize();
    void startContinuousMonitoring();
    void stopContinuousMonitoring();
    
private:
    PerfettoLLMTracer() = default;
    ~PerfettoLLMTracer() = default;
    PerfettoLLMTracer(const PerfettoLLMTracer&) = delete;
    PerfettoLLMTracer& operator=(const PerfettoLLMTracer&) = delete;

    std::atomic<bool> is_tracing{false};
    std::atomic<bool> continuous_monitoring{false};
    std::unique_ptr<std::thread> monitoring_thread;

    // Performance tracking
    std::chrono::steady_clock::time_point inference_start_time;
    std::chrono::steady_clock::time_point prefill_start_time;
    std::chrono::steady_clock::time_point decode_start_time;

    // CPU tracking state
    struct CpuState {
        long total_time = 0;
        long idle_time = 0;
        long process_utime = 0;
        long process_stime = 0;
        std::chrono::steady_clock::time_point timestamp;
        bool valid = false;
    };

    CpuState prev_system_cpu;
    CpuState prev_process_cpu;

    void continuousMonitoringLoop();
    bool readSystemCpuStats(CpuState& state);
    bool readProcessCpuStats(CpuState& state);
};

#endif // PERFETTO_LLM_INTEGRATION_H