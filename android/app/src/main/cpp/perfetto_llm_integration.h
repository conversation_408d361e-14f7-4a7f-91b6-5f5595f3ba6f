#ifndef PERFETTO_LLM_INTEGRATION_H
#define PERFETTO_LLM_INTEGRATION_H

#include <string>
#include <atomic>
#include <chrono>
#include <thread>
#include <memory>

class PerfettoLLMTracer {
public:
    static PerfettoLLMTracer& getInstance();
    
    // Core tracing operations
    bool initialize();
    bool startTrace(const std::string& categories);
    bool stopTrace();
    bool isTracing() const;
    
    // LLM-specific trace events (Option 1 & 2 compatible)
    void traceInferenceStart(const std::string& model_name, int context_length);
    void traceInferenceEnd();
    void tracePrefillStart(int token_count);
    void tracePrefillEnd();
    void traceDecodeStart();
    void traceDecodeEnd();
    void traceTokenGenerated(const std::string& token, int token_id);
    
    // System metrics (for academic analysis)
    void recordCpuUsage(double cpu_percent);
    void recordMemoryUsage(long memory_bytes);
    void recordSystemMetrics();
    
private:
    PerfettoLLMTracer() = default;
    ~PerfettoLLMTracer() = default;
    
    std::atomic<bool> is_tracing{false};
    
    // Performance tracking
    std::chrono::steady_clock::time_point inference_start_time;
    std::chrono::steady_clock::time_point prefill_start_time;
    std::chrono::steady_clock::time_point decode_start_time;
    
    // ADD THESE METHOD DECLARATIONS:
    double getCurrentCpuUsage();
    long getCurrentMemoryUsage();
    long getProcessMemoryUsage();
};

#endif // PERFETTO_LLM_INTEGRATION_H