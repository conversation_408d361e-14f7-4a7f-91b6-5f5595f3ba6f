#include "perfetto_llm_integration.h"
#include <android/log.h>
#include <chrono>
#include <fstream>
#include <sstream>
#include <thread>

// Include Android trace header
#include <android/trace.h>

// Define missing constants for older API levels
#ifndef ATRACE_TAG_APP
#define ATRACE_TAG_APP (1 << 12)
#endif

#ifndef ATRACE_TAG_GRAPHICS
#define ATRACE_TAG_GRAPHICS (1 << 1)
#endif

#ifndef ATRACE_TAG_PERF
#define ATRACE_TAG_PERF (1 << 21)
#endif

#define LOG_TAG "PerfettoLLMTracer"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// Static variables for CPU calculation
static long prev_total_cpu = 0;
static long prev_idle_cpu = 0;
static bool cpu_baseline_set = false;

// Compatibility wrappers for different Android API levels
void compat_ATrace_setCounter(const char* name, int64_t value) {
    #if __ANDROID_API__ >= 29
        ATrace_setCounter(name, value);
    #else
        // For older APIs, log the counter value instead
        LOGI("COUNTER: %s = %lld", name, (long long)value);
    #endif
}

void compat_ATrace_beginAsyncSection(const char* name, int32_t cookie) {
    #if __ANDROID_API__ >= 29
        ATrace_beginAsyncSection(name, cookie);
    #else
        // Fallback to regular section for older APIs
        ATrace_beginSection(name);
    #endif
}

void compat_ATrace_endAsyncSection(const char* name, int32_t cookie) {
    #if __ANDROID_API__ >= 29
        ATrace_endAsyncSection(name, cookie);
    #else
        // Fallback to regular section end for older APIs
        ATrace_endSection();
    #endif
}

PerfettoLLMTracer& PerfettoLLMTracer::getInstance() {
    static PerfettoLLMTracer instance;
    return instance;
}

bool PerfettoLLMTracer::initialize() {
    LOGI("Initializing REAL Perfetto LLM Tracer with Android Trace API (API Level: %d)", __ANDROID_API__);
    
    // Initialize CPU baseline
    getCurrentCpuUsage(); // This call sets the baseline
    cpu_baseline_set = true;
    
    LOGI("Perfetto LLM Tracer initialized successfully with Android Trace API");
    return true;
}

bool PerfettoLLMTracer::startTrace(const std::string& categories) {
    if (is_tracing.load()) {
        LOGI("Tracing already active");
        return true;
    }
    
    LOGI("Starting Perfetto-compatible trace with categories: %s", categories.c_str());
    
    // Android tracing is enabled by default - no need for ATrace_setEnabledTags
    is_tracing.store(true);
    LOGI("Perfetto-compatible trace started - events will feed into Perfetto");
    return true;
}

bool PerfettoLLMTracer::stopTrace() {
    if (!is_tracing.load()) {
        LOGI("No active tracing session");
        return true;
    }
    
    LOGI("Stopping REAL Perfetto trace");
    is_tracing.store(false);
    LOGI("Real Perfetto trace stopped - collect with: adb shell perfetto -c - --txt -o /data/misc/perfetto-traces/trace");
    return true;
}

bool PerfettoLLMTracer::isTracing() const {
    return is_tracing.load();
}

void PerfettoLLMTracer::traceInferenceStart(const std::string& model_name, int context_length) {
    if (!is_tracing.load()) return;
    
    inference_start_time = std::chrono::steady_clock::now();
    
    // REAL Perfetto trace event
    std::string trace_name = "LLM_Inference_" + model_name;
    ATrace_beginSection(trace_name.c_str());
    
    LOGI("TRACE: Inference Start - Model: %s, Context Length: %d", 
         model_name.c_str(), context_length);
}

void PerfettoLLMTracer::traceInferenceEnd() {
    if (!is_tracing.load()) return;
    
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
        end_time - inference_start_time).count();
    
    // End REAL Perfetto trace event
    ATrace_endSection();
    
    LOGI("TRACE: Inference End - Duration: %lld μs", (long long)duration);
}

void PerfettoLLMTracer::tracePrefillStart(int token_count) {
    if (!is_tracing.load()) return;
    
    prefill_start_time = std::chrono::steady_clock::now();
    
    // REAL Perfetto trace event
    ATrace_beginSection("LLM_Prefill_Phase");
    
    LOGI("TRACE: Prefill Start - Token Count: %d", token_count);
    recordSystemMetrics();
}

void PerfettoLLMTracer::tracePrefillEnd() {
    if (!is_tracing.load()) return;
    
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
        end_time - prefill_start_time).count();
    
    // End REAL Perfetto trace event
    ATrace_endSection();
    
    LOGI("TRACE: Prefill End - Duration: %lld μs", (long long)duration);
    recordSystemMetrics();
}

void PerfettoLLMTracer::traceDecodeStart() {
    if (!is_tracing.load()) return;
    
    decode_start_time = std::chrono::steady_clock::now();
    
    // REAL Perfetto trace event
    ATrace_beginSection("LLM_Decode_Phase");
    
    LOGI("TRACE: Decode Start");
    recordSystemMetrics();
}

void PerfettoLLMTracer::traceDecodeEnd() {
    if (!is_tracing.load()) return;
    
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
        end_time - decode_start_time).count();
    
    // End REAL Perfetto trace event
    ATrace_endSection();
    
    LOGI("TRACE: Decode End - Duration: %lld μs", (long long)duration);
    recordSystemMetrics();
}

void PerfettoLLMTracer::traceTokenGenerated(const std::string& token, int token_id) {
    if (!is_tracing.load()) return;
    
    // REAL Perfetto instant event (every 10th token to avoid spam)
    if (token_id % 10 == 0) {
        std::string event_name = "Token_" + std::to_string(token_id);
        ATrace_beginSection(event_name.c_str());
        ATrace_endSection();
    }
    
    LOGI("TRACE: Token Generated - ID: %d, Token: %s", token_id, token.c_str());
}

void PerfettoLLMTracer::recordCpuUsage(double cpu_percent) {
    if (!is_tracing.load()) return;
    
    // Use compatibility wrapper for counter
    compat_ATrace_setCounter("CPU_Usage_Percent", (int64_t)(cpu_percent * 100));
    
    LOGI("TRACE: CPU Usage: %.2f%%", cpu_percent);
}

void PerfettoLLMTracer::recordMemoryUsage(long memory_bytes) {
    if (!is_tracing.load()) return;
    
    long memory_mb = memory_bytes / (1024 * 1024);
    long process_memory = getProcessMemoryUsage();
    long process_mb = process_memory / (1024 * 1024);
    
    // Use compatibility wrappers for counters
    compat_ATrace_setCounter("System_Memory_MB", memory_mb);
    compat_ATrace_setCounter("Process_Memory_MB", process_mb);
    
    LOGI("TRACE: System Memory Used: %ld MB, Process Memory: %ld MB", memory_mb, process_mb);
}

void PerfettoLLMTracer::recordSystemMetrics() {
    if (!is_tracing.load()) return;
    
    // Get real CPU usage with proper timing
    double cpuUsage = getCurrentCpuUsage();
    recordCpuUsage(cpuUsage);
    
    // Get real memory usage
    long memoryUsage = getCurrentMemoryUsage();
    recordMemoryUsage(memoryUsage);
}

double PerfettoLLMTracer::getCurrentCpuUsage() {
    try {
        // Read /proc/stat for system-wide CPU usage
        std::ifstream stat_file("/proc/stat");
        if (!stat_file.is_open()) {
            return 0.0;
        }
        
        std::string line;
        std::getline(stat_file, line);
        stat_file.close();
        
        if (line.substr(0, 4) != "cpu ") {
            return 0.0;
        }
        
        // Parse CPU times: user, nice, system, idle, iowait, irq, softirq
        std::istringstream iss(line);
        std::string cpu_label;
        long user, nice, system, idle, iowait, irq, softirq;
        
        iss >> cpu_label >> user >> nice >> system >> idle >> iowait >> irq >> softirq;
        
        long total_time = user + nice + system + idle + iowait + irq + softirq;
        long idle_time = idle + iowait;
        
        // Calculate CPU usage based on delta since last measurement
        if (cpu_baseline_set && prev_total_cpu != 0) {
            long total_diff = total_time - prev_total_cpu;
            long idle_diff = idle_time - prev_idle_cpu;
            
            if (total_diff > 0) {
                double cpu_percent = 100.0 * (total_diff - idle_diff) / total_diff;
                prev_total_cpu = total_time;
                prev_idle_cpu = idle_time;
                return std::max(0.0, std::min(100.0, cpu_percent)); // Clamp between 0-100
            }
        }
        
        prev_total_cpu = total_time;
        prev_idle_cpu = idle_time;
        return 0.0;
        
    } catch (const std::exception& e) {
        LOGE("Error reading CPU usage: %s", e.what());
        return 0.0;
    }
}

long PerfettoLLMTracer::getCurrentMemoryUsage() {
    try {
        std::ifstream meminfo_file("/proc/meminfo");
        if (!meminfo_file.is_open()) {
            return 0;
        }
        
        std::string line;
        long total_mem = 0, free_mem = 0, available_mem = 0, buffers = 0, cached = 0;
        
        while (std::getline(meminfo_file, line)) {
            std::istringstream iss(line);
            std::string key;
            long value;
            std::string unit;
            
            if (iss >> key >> value >> unit) {
                if (key == "MemTotal:") {
                    total_mem = value * 1024;
                } else if (key == "MemFree:") {
                    free_mem = value * 1024;
                } else if (key == "MemAvailable:") {
                    available_mem = value * 1024;
                } else if (key == "Buffers:") {
                    buffers = value * 1024;
                } else if (key == "Cached:") {
                    cached = value * 1024;
                }
            }
        }
        meminfo_file.close();
        
        long used_mem = total_mem - free_mem - buffers - cached;
        return used_mem > 0 ? used_mem : total_mem - free_mem;
        
    } catch (const std::exception& e) {
        LOGE("Error reading memory usage: %s", e.what());
        return 0;
    }
}

long PerfettoLLMTracer::getProcessMemoryUsage() {
    try {
        std::ifstream status_file("/proc/self/status");
        if (!status_file.is_open()) {
            return 0;
        }
        
        std::string line;
        long vm_rss = 0;
        
        while (std::getline(status_file, line)) {
            if (line.substr(0, 6) == "VmRSS:") {
                std::istringstream iss(line);
                std::string key;
                long value;
                std::string unit;
                
                if (iss >> key >> value >> unit) {
                    vm_rss = value * 1024;
                    break;
                }
            }
        }
        status_file.close();
        
        return vm_rss;
        
    } catch (const std::exception& e) {
        LOGE("Error reading process memory: %s", e.what());
        return 0;
    }
}