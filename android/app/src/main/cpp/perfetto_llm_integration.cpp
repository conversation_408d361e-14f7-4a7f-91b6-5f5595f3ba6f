#include "perfetto_llm_integration.h"
#include <android/log.h>
#include <chrono>
#include <fstream>
#include <sstream>
#include <thread>
#include <unistd.h>

// Include Android trace header
#include <android/trace.h>

// Define missing constants for older API levels
#ifndef ATRACE_TAG_APP
#define ATRACE_TAG_APP (1 << 12)
#endif

#ifndef ATRACE_TAG_GRAPHICS
#define ATRACE_TAG_GRAPHICS (1 << 1)
#endif

#ifndef ATRACE_TAG_PERF
#define ATRACE_TAG_PERF (1 << 21)
#endif

#define LOG_TAG "PerfettoLLMTracer"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// Remove static variables - now using instance variables

// Compatibility wrappers for different Android API levels
void compat_ATrace_setCounter(const char* name, int64_t value) {
    #if __ANDROID_API__ >= 29
        ATrace_setCounter(name, value);
    #else
        // For older APIs, log the counter value instead
        LOGI("COUNTER: %s = %lld", name, (long long)value);
    #endif
}

void compat_ATrace_beginAsyncSection(const char* name, int32_t cookie) {
    #if __ANDROID_API__ >= 29
        ATrace_beginAsyncSection(name, cookie);
    #else
        // Fallback to regular section for older APIs
        ATrace_beginSection(name);
    #endif
}

void compat_ATrace_endAsyncSection(const char* name, int32_t cookie) {
    #if __ANDROID_API__ >= 29
        ATrace_endAsyncSection(name, cookie);
    #else
        // Fallback to regular section end for older APIs
        ATrace_endSection();
    #endif
}

PerfettoLLMTracer& PerfettoLLMTracer::getInstance() {
    static PerfettoLLMTracer instance;
    return instance;
}

bool PerfettoLLMTracer::initialize() {
    LOGI("Initializing REAL Perfetto LLM Tracer with Android Trace API (API Level: %d)", __ANDROID_API__);

    // Initialize CPU baselines
    readSystemCpuStats(prev_system_cpu);
    readProcessCpuStats(prev_process_cpu);

    LOGI("Perfetto LLM Tracer initialized successfully with Android Trace API");
    return true;
}

bool PerfettoLLMTracer::startTrace(const std::string& categories) {
    if (is_tracing.load()) {
        LOGI("Tracing already active");
        return true;
    }
    
    LOGI("Starting Perfetto-compatible trace with categories: %s", categories.c_str());
    
    // Android tracing is enabled by default - no need for ATrace_setEnabledTags
    is_tracing.store(true);
    LOGI("Perfetto-compatible trace started - events will feed into Perfetto");
    return true;
}

bool PerfettoLLMTracer::stopTrace() {
    if (!is_tracing.load()) {
        LOGI("No active tracing session");
        return true;
    }

    LOGI("Stopping REAL Perfetto trace");
    stopContinuousMonitoring();
    is_tracing.store(false);
    LOGI("Real Perfetto trace stopped - collect with: adb shell perfetto -c - --txt -o /data/misc/perfetto-traces/trace");
    return true;
}

bool PerfettoLLMTracer::isTracing() const {
    return is_tracing.load();
}

void PerfettoLLMTracer::traceInferenceStart(const std::string& model_name, int context_length) {
    if (!is_tracing.load()) return;
    
    inference_start_time = std::chrono::steady_clock::now();
    
    // REAL Perfetto trace event
    std::string trace_name = "LLM_Inference_" + model_name;
    ATrace_beginSection(trace_name.c_str());
    
    LOGI("TRACE: Inference Start - Model: %s, Context Length: %d", 
         model_name.c_str(), context_length);
}

void PerfettoLLMTracer::traceInferenceEnd() {
    if (!is_tracing.load()) return;
    
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
        end_time - inference_start_time).count();
    
    // End REAL Perfetto trace event
    ATrace_endSection();
    
    LOGI("TRACE: Inference End - Duration: %lld μs", (long long)duration);
}

void PerfettoLLMTracer::tracePrefillStart(int token_count) {
    if (!is_tracing.load()) return;

    prefill_start_time = std::chrono::steady_clock::now();

    // REAL Perfetto trace event
    ATrace_beginSection("LLM_Prefill_Phase");

    LOGI("TRACE: Prefill Start - Token Count: %d", token_count);
    startContinuousMonitoring();
    recordSystemMetrics();
}

void PerfettoLLMTracer::tracePrefillEnd() {
    if (!is_tracing.load()) return;
    
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
        end_time - prefill_start_time).count();
    
    // End REAL Perfetto trace event
    ATrace_endSection();
    
    LOGI("TRACE: Prefill End - Duration: %lld μs", (long long)duration);
    recordSystemMetrics();
}

void PerfettoLLMTracer::traceDecodeStart() {
    if (!is_tracing.load()) return;
    
    decode_start_time = std::chrono::steady_clock::now();
    
    // REAL Perfetto trace event
    ATrace_beginSection("LLM_Decode_Phase");
    
    LOGI("TRACE: Decode Start");
    recordSystemMetrics();
}

void PerfettoLLMTracer::traceDecodeEnd() {
    if (!is_tracing.load()) return;
    
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
        end_time - decode_start_time).count();
    
    // End REAL Perfetto trace event
    ATrace_endSection();
    
    LOGI("TRACE: Decode End - Duration: %lld μs", (long long)duration);
    recordSystemMetrics();
}

void PerfettoLLMTracer::traceTokenGenerated(const std::string& token, int token_id) {
    if (!is_tracing.load()) return;
    
    // REAL Perfetto instant event (every 10th token to avoid spam)
    if (token_id % 10 == 0) {
        std::string event_name = "Token_" + std::to_string(token_id);
        ATrace_beginSection(event_name.c_str());
        ATrace_endSection();
    }
    
    LOGI("TRACE: Token Generated - ID: %d, Token: %s", token_id, token.c_str());
}

void PerfettoLLMTracer::recordCpuUsage(double cpu_percent) {
    if (!is_tracing.load()) return;

    // Use compatibility wrapper for counter
    compat_ATrace_setCounter("System_CPU_Usage_Percent", (int64_t)(cpu_percent * 100));

    // Also record process CPU usage
    double process_cpu = getProcessCpuUsage();
    compat_ATrace_setCounter("Process_CPU_Usage_Percent", (int64_t)(process_cpu * 100));

    LOGI("TRACE: System CPU: %.2f%%, Process CPU: %.2f%%", cpu_percent, process_cpu);
}

void PerfettoLLMTracer::recordMemoryUsage(long memory_bytes) {
    if (!is_tracing.load()) return;

    long memory_mb = memory_bytes / (1024 * 1024);
    long process_memory = getProcessMemoryUsage();
    long process_mb = process_memory / (1024 * 1024);
    long process_vm = getProcessVmSize();
    long process_vm_mb = process_vm / (1024 * 1024);

    // Use compatibility wrappers for counters
    compat_ATrace_setCounter("System_Memory_Used_MB", memory_mb);
    compat_ATrace_setCounter("Process_RSS_MB", process_mb);
    compat_ATrace_setCounter("Process_VmSize_MB", process_vm_mb);

    LOGI("TRACE: System Memory: %ld MB, Process RSS: %ld MB, Process VmSize: %ld MB",
         memory_mb, process_mb, process_vm_mb);
}

void PerfettoLLMTracer::recordSystemMetrics() {
    if (!is_tracing.load()) return;
    
    // Get real CPU usage with proper timing
    double cpuUsage = getCurrentCpuUsage();
    recordCpuUsage(cpuUsage);
    
    // Get real memory usage
    long memoryUsage = getCurrentMemoryUsage();
    recordMemoryUsage(memoryUsage);
}

bool PerfettoLLMTracer::readSystemCpuStats(CpuState& state) {
    try {
        std::ifstream stat_file("/proc/stat");
        if (!stat_file.is_open()) {
            return false;
        }

        std::string line;
        std::getline(stat_file, line);
        stat_file.close();

        if (line.substr(0, 4) != "cpu ") {
            return false;
        }

        // Parse CPU times: user, nice, system, idle, iowait, irq, softirq
        std::istringstream iss(line);
        std::string cpu_label;
        long user, nice, system, idle, iowait, irq, softirq;

        iss >> cpu_label >> user >> nice >> system >> idle >> iowait >> irq >> softirq;

        state.total_time = user + nice + system + idle + iowait + irq + softirq;
        state.idle_time = idle + iowait;
        state.timestamp = std::chrono::steady_clock::now();
        state.valid = true;

        return true;
    } catch (const std::exception& e) {
        LOGE("Error reading system CPU stats: %s", e.what());
        return false;
    }
}

bool PerfettoLLMTracer::readProcessCpuStats(CpuState& state) {
    try {
        std::ifstream stat_file("/proc/self/stat");
        if (!stat_file.is_open()) {
            return false;
        }

        std::string line;
        std::getline(stat_file, line);
        stat_file.close();

        // Parse /proc/self/stat - fields 14 and 15 are utime and stime
        std::istringstream iss(line);
        std::string field;
        for (int i = 0; i < 13; i++) {
            iss >> field; // Skip to field 14 (utime)
        }

        long utime, stime;
        iss >> utime >> stime;

        state.process_utime = utime;
        state.process_stime = stime;
        state.timestamp = std::chrono::steady_clock::now();
        state.valid = true;

        return true;
    } catch (const std::exception& e) {
        LOGE("Error reading process CPU stats: %s", e.what());
        return false;
    }
}

double PerfettoLLMTracer::getCurrentCpuUsage() {
    CpuState current_state;
    if (!readSystemCpuStats(current_state)) {
        return 0.0;
    }

    // If this is the first reading, just store it and return 0
    if (!prev_system_cpu.valid) {
        prev_system_cpu = current_state;
        return 0.0;
    }

    long total_diff = current_state.total_time - prev_system_cpu.total_time;
    long idle_diff = current_state.idle_time - prev_system_cpu.idle_time;

    double cpu_percent = 0.0;
    if (total_diff > 0) {
        cpu_percent = 100.0 * (total_diff - idle_diff) / total_diff;
        cpu_percent = std::max(0.0, std::min(100.0, cpu_percent));

        // Debug logging (remove after testing)
        LOGI("System CPU Debug: total_diff=%ld, idle_diff=%ld, active_diff=%ld, cpu_percent=%.2f",
             total_diff, idle_diff, (total_diff - idle_diff), cpu_percent);
    }

    prev_system_cpu = current_state;
    return cpu_percent;
}

double PerfettoLLMTracer::getProcessCpuUsage() {
    CpuState current_state;
    if (!readProcessCpuStats(current_state)) {
        return 0.0;
    }

    // If this is the first reading, just store it and return 0
    if (!prev_process_cpu.valid) {
        prev_process_cpu = current_state;
        return 0.0;
    }

    // Calculate time difference in seconds
    auto time_diff_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        current_state.timestamp - prev_process_cpu.timestamp).count();

    if (time_diff_ms <= 0) {
        prev_process_cpu = current_state;
        return 0.0;
    }

    double time_diff_sec = time_diff_ms / 1000.0;

    // Calculate CPU time difference (in clock ticks)
    long cpu_time_diff = (current_state.process_utime + current_state.process_stime) -
                        (prev_process_cpu.process_utime + prev_process_cpu.process_stime);

    // Get system clock ticks per second (usually 100 on Android)
    static long clock_ticks_per_sec = sysconf(_SC_CLK_TCK);
    if (clock_ticks_per_sec <= 0) {
        clock_ticks_per_sec = 100; // fallback
    }

    // Get number of CPU cores to normalize the percentage
    static long num_cores = sysconf(_SC_NPROCESSORS_ONLN);
    if (num_cores <= 0) {
        num_cores = 1; // fallback
    }

    // Convert CPU ticks to seconds, then to percentage
    double cpu_time_sec = (double)cpu_time_diff / clock_ticks_per_sec;
    double cpu_percent = (cpu_time_sec / time_diff_sec) * 100.0;

    // Normalize by number of cores (process can use multiple cores)
    // But cap at 100% for single-threaded equivalent
    cpu_percent = cpu_percent / num_cores;

    // Debug logging (remove after testing)
    if (cpu_percent > 5.0) {  // Only log significant CPU usage
        LOGI("Process CPU Debug: ticks_diff=%ld, time_diff=%.3fs, cores=%ld, raw_percent=%.2f, normalized=%.2f",
             cpu_time_diff, time_diff_sec, num_cores, (cpu_time_sec / time_diff_sec) * 100.0, cpu_percent);
    }

    prev_process_cpu = current_state;
    return std::max(0.0, std::min(100.0, cpu_percent));
}

long PerfettoLLMTracer::getCurrentMemoryUsage() {
    try {
        std::ifstream meminfo_file("/proc/meminfo");
        if (!meminfo_file.is_open()) {
            return 0;
        }
        
        std::string line;
        long total_mem = 0, free_mem = 0, available_mem = 0, buffers = 0, cached = 0;
        
        while (std::getline(meminfo_file, line)) {
            std::istringstream iss(line);
            std::string key;
            long value;
            std::string unit;
            
            if (iss >> key >> value >> unit) {
                if (key == "MemTotal:") {
                    total_mem = value * 1024;
                } else if (key == "MemFree:") {
                    free_mem = value * 1024;
                } else if (key == "MemAvailable:") {
                    available_mem = value * 1024;
                } else if (key == "Buffers:") {
                    buffers = value * 1024;
                } else if (key == "Cached:") {
                    cached = value * 1024;
                }
            }
        }
        meminfo_file.close();
        
        long used_mem = total_mem - free_mem - buffers - cached;
        return used_mem > 0 ? used_mem : total_mem - free_mem;
        
    } catch (const std::exception& e) {
        LOGE("Error reading memory usage: %s", e.what());
        return 0;
    }
}

long PerfettoLLMTracer::getProcessMemoryUsage() {
    try {
        std::ifstream status_file("/proc/self/status");
        if (!status_file.is_open()) {
            return 0;
        }

        std::string line;
        long vm_rss = 0;

        while (std::getline(status_file, line)) {
            if (line.substr(0, 6) == "VmRSS:") {
                std::istringstream iss(line);
                std::string key;
                long value;
                std::string unit;

                if (iss >> key >> value >> unit) {
                    vm_rss = value * 1024;
                    break;
                }
            }
        }
        status_file.close();

        return vm_rss;

    } catch (const std::exception& e) {
        LOGE("Error reading process memory: %s", e.what());
        return 0;
    }
}

long PerfettoLLMTracer::getProcessVmSize() {
    try {
        std::ifstream status_file("/proc/self/status");
        if (!status_file.is_open()) {
            return 0;
        }

        std::string line;
        long vm_size = 0;

        while (std::getline(status_file, line)) {
            if (line.substr(0, 7) == "VmSize:") {
                std::istringstream iss(line);
                std::string key;
                long value;
                std::string unit;

                if (iss >> key >> value >> unit) {
                    vm_size = value * 1024;
                    break;
                }
            }
        }
        status_file.close();

        return vm_size;

    } catch (const std::exception& e) {
        LOGE("Error reading process VmSize: %s", e.what());
        return 0;
    }
}

void PerfettoLLMTracer::startContinuousMonitoring() {
    if (continuous_monitoring.load()) {
        return; // Already running
    }

    continuous_monitoring.store(true);
    monitoring_thread = std::make_unique<std::thread>(&PerfettoLLMTracer::continuousMonitoringLoop, this);
    LOGI("Started continuous monitoring thread");
}

void PerfettoLLMTracer::stopContinuousMonitoring() {
    if (!continuous_monitoring.load()) {
        return; // Not running
    }

    continuous_monitoring.store(false);
    if (monitoring_thread && monitoring_thread->joinable()) {
        monitoring_thread->join();
    }
    monitoring_thread.reset();
    LOGI("Stopped continuous monitoring thread");
}

void PerfettoLLMTracer::continuousMonitoringLoop() {
    LOGI("Continuous monitoring loop started");

    while (continuous_monitoring.load()) {
        if (is_tracing.load()) {
            recordSystemMetrics();
        }

        // Sleep for 500ms between measurements
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }

    LOGI("Continuous monitoring loop ended");
}