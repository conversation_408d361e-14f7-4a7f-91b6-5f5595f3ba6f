#include <jsi/jsi.h>
#include <jni.h>  // Add this line for JNIEXPORT and JNICALL
#include <android/log.h>
#include "perfetto_llm_integration.h"

using namespace facebook::jsi;
using namespace std;

#define LOG_TAG "PerfettoJSI"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)

class PerfettoLLMHostObject : public HostObject {
public:
    Value get(Runtime& runtime, const PropNameID& propName) override {
        auto name = propName.utf8(runtime);
        
        if (name == "initialize") {
            return Function::createFromHostFunction(
                runtime, PropNameID::forAscii(runtime, "initialize"), 0,
                [](Runtime& runtime, const Value& thisValue, const Value* arguments, size_t count) -> Value {
                    bool success = PerfettoLLMTracer::getInstance().initialize();
                    return Value(success);
                });
        }
        
        if (name == "startTrace") {
            return Function::createFromHostFunction(
                runtime, PropNameID::forAscii(runtime, "startTrace"), 1,
                [](Runtime& runtime, const Value& thisValue, const Value* arguments, size_t count) -> Value {
                    if (count > 0 && arguments[0].isString()) {
                        string categories = arguments[0].getString(runtime).utf8(runtime);
                        bool success = PerfettoLLMTracer::getInstance().startTrace(categories);
                        return Value(success);
                    }
                    return Value(false);
                });
        }
        
        if (name == "stopTrace") {
            return Function::createFromHostFunction(
                runtime, PropNameID::forAscii(runtime, "stopTrace"), 0,
                [](Runtime& runtime, const Value& thisValue, const Value* arguments, size_t count) -> Value {
                    bool success = PerfettoLLMTracer::getInstance().stopTrace();
                    return Value(success);
                });
        }
        
        if (name == "isTracing") {
            return Function::createFromHostFunction(
                runtime, PropNameID::forAscii(runtime, "isTracing"), 0,
                [](Runtime& runtime, const Value& thisValue, const Value* arguments, size_t count) -> Value {
                    bool tracing = PerfettoLLMTracer::getInstance().isTracing();
                    return Value(tracing);
                });
        }
        
        if (name == "traceInferenceStart") {
            return Function::createFromHostFunction(
                runtime, PropNameID::forAscii(runtime, "traceInferenceStart"), 2,
                [](Runtime& runtime, const Value& thisValue, const Value* arguments, size_t count) -> Value {
                    if (count >= 2 && arguments[0].isString() && arguments[1].isNumber()) {
                        string modelName = arguments[0].getString(runtime).utf8(runtime);
                        int contextLength = (int)arguments[1].getNumber();
                        PerfettoLLMTracer::getInstance().traceInferenceStart(modelName, contextLength);
                    }
                    return Value::undefined();
                });
        }
        
        if (name == "traceInferenceEnd") {
            return Function::createFromHostFunction(
                runtime, PropNameID::forAscii(runtime, "traceInferenceEnd"), 0,
                [](Runtime& runtime, const Value& thisValue, const Value* arguments, size_t count) -> Value {
                    PerfettoLLMTracer::getInstance().traceInferenceEnd();
                    return Value::undefined();
                });
        }
        
        if (name == "tracePrefillStart") {
            return Function::createFromHostFunction(
                runtime, PropNameID::forAscii(runtime, "tracePrefillStart"), 1,
                [](Runtime& runtime, const Value& thisValue, const Value* arguments, size_t count) -> Value {
                    if (count > 0 && arguments[0].isNumber()) {
                        int tokenCount = (int)arguments[0].getNumber();
                        PerfettoLLMTracer::getInstance().tracePrefillStart(tokenCount);
                    }
                    return Value::undefined();
                });
        }
        
        if (name == "tracePrefillEnd") {
            return Function::createFromHostFunction(
                runtime, PropNameID::forAscii(runtime, "tracePrefillEnd"), 0,
                [](Runtime& runtime, const Value& thisValue, const Value* arguments, size_t count) -> Value {
                    PerfettoLLMTracer::getInstance().tracePrefillEnd();
                    return Value::undefined();
                });
        }
        
        if (name == "traceDecodeStart") {
            return Function::createFromHostFunction(
                runtime, PropNameID::forAscii(runtime, "traceDecodeStart"), 0,
                [](Runtime& runtime, const Value& thisValue, const Value* arguments, size_t count) -> Value {
                    PerfettoLLMTracer::getInstance().traceDecodeStart();
                    return Value::undefined();
                });
        }
        
        if (name == "traceDecodeEnd") {
            return Function::createFromHostFunction(
                runtime, PropNameID::forAscii(runtime, "traceDecodeEnd"), 0,
                [](Runtime& runtime, const Value& thisValue, const Value* arguments, size_t count) -> Value {
                    PerfettoLLMTracer::getInstance().traceDecodeEnd();
                    return Value::undefined();
                });
        }
        
        if (name == "traceTokenGenerated") {
            return Function::createFromHostFunction(
                runtime, PropNameID::forAscii(runtime, "traceTokenGenerated"), 2,
                [](Runtime& runtime, const Value& thisValue, const Value* arguments, size_t count) -> Value {
                    if (count >= 2 && arguments[0].isString() && arguments[1].isNumber()) {
                        string token = arguments[0].getString(runtime).utf8(runtime);
                        int tokenId = (int)arguments[1].getNumber();
                        PerfettoLLMTracer::getInstance().traceTokenGenerated(token, tokenId);
                    }
                    return Value::undefined();
                });
        }

        if (name == "getCurrentCpuUsage") {
            return Function::createFromHostFunction(
                runtime, PropNameID::forAscii(runtime, "getCurrentCpuUsage"), 0,
                [](Runtime& runtime, const Value& thisValue, const Value* arguments, size_t count) -> Value {
                    double cpu = PerfettoLLMTracer::getInstance().getCurrentCpuUsage();
                    return Value(cpu);
                });
        }

        if (name == "getProcessCpuUsage") {
            return Function::createFromHostFunction(
                runtime, PropNameID::forAscii(runtime, "getProcessCpuUsage"), 0,
                [](Runtime& runtime, const Value& thisValue, const Value* arguments, size_t count) -> Value {
                    double cpu = PerfettoLLMTracer::getInstance().getProcessCpuUsage();
                    return Value(cpu);
                });
        }

        if (name == "getCurrentMemoryUsage") {
            return Function::createFromHostFunction(
                runtime, PropNameID::forAscii(runtime, "getCurrentMemoryUsage"), 0,
                [](Runtime& runtime, const Value& thisValue, const Value* arguments, size_t count) -> Value {
                    long memory = PerfettoLLMTracer::getInstance().getCurrentMemoryUsage();
                    return Value((double)memory);
                });
        }

        if (name == "getProcessMemoryUsage") {
            return Function::createFromHostFunction(
                runtime, PropNameID::forAscii(runtime, "getProcessMemoryUsage"), 0,
                [](Runtime& runtime, const Value& thisValue, const Value* arguments, size_t count) -> Value {
                    long memory = PerfettoLLMTracer::getInstance().getProcessMemoryUsage();
                    return Value((double)memory);
                });
        }

        if (name == "startContinuousMonitoring") {
            return Function::createFromHostFunction(
                runtime, PropNameID::forAscii(runtime, "startContinuousMonitoring"), 0,
                [](Runtime& runtime, const Value& thisValue, const Value* arguments, size_t count) -> Value {
                    PerfettoLLMTracer::getInstance().startContinuousMonitoring();
                    return Value::undefined();
                });
        }

        if (name == "stopContinuousMonitoring") {
            return Function::createFromHostFunction(
                runtime, PropNameID::forAscii(runtime, "stopContinuousMonitoring"), 0,
                [](Runtime& runtime, const Value& thisValue, const Value* arguments, size_t count) -> Value {
                    PerfettoLLMTracer::getInstance().stopContinuousMonitoring();
                    return Value::undefined();
                });
        }

        return Value::undefined();
    }
};

extern "C" JNIEXPORT void JNICALL
Java_com_smartva_PerfettoLLMModule_installJSIBindings(JNIEnv *env, jobject thiz, jlong jsiPtr) {
    auto runtime = reinterpret_cast<Runtime*>(jsiPtr);
    auto hostObject = make_shared<PerfettoLLMHostObject>();
    runtime->global().setProperty(*runtime, "PerfettoLLMTracer", Object::createFromHostObject(*runtime, hostObject));
    LOGI("PerfettoLLMTracer JSI bindings installed");
}