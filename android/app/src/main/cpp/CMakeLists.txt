cmake_minimum_required(VERSION 3.13.0)

project(smartva_native_modules)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_VERBOSE_MAKEFILE ON)

# Find required packages
find_library(log-lib log)

# Add library search path for the Rust library (your existing config)
link_directories(${CMAKE_CURRENT_SOURCE_DIR}/../jniLibs/${ANDROID_ABI})

# Add Perfetto SDK path
set(PERFETTO_SDK_PATH "${CMAKE_CURRENT_SOURCE_DIR}/perfetto")
include_directories(${PERFETTO_SDK_PATH}/include)

# Set up JSI includes (same as onnxruntime)
set(NODE_MODULES_DIR "${CMAKE_SOURCE_DIR}/../../../../../node_modules")
include_directories(
    "${NODE_MODULES_DIR}/react-native/React"
    "${NODE_MODULES_DIR}/react-native/React/Base"
    "${NODE_MODULES_DIR}/react-native/ReactCommon/jsi"
)

# Get JSI source file (same as onnxruntime)
file(TO_CMAKE_PATH "${NODE_MODULES_DIR}/react-native/ReactCommon/jsi/jsi/jsi.cpp" JSI_LIB_PATH)

# Your existing HNSW library (unchanged)
add_library(
    hnsw_native
    SHARED
    hnsw_jni.cpp
)

# Perfetto LLM library using JSI
add_library(
    perfetto_llm_native
    SHARED
    ${JSI_LIB_PATH}
    perfetto_llm_integration.cpp
    jsi_bridge.cpp
)

# Configure C++17 for Perfetto module
set_target_properties(
    perfetto_llm_native PROPERTIES
    CXX_STANDARD 17
    CXX_EXTENSIONS OFF
    POSITION_INDEPENDENT_CODE ON
)

# Link libraries for HNSW (your existing config)
target_link_libraries(hnsw_native
    ${log-lib}
    hnsw_lib
)

# Link libraries for Perfetto (JSI approach)
target_link_libraries(perfetto_llm_native
    ${log-lib}
    android
)

# Add Android API level definition for trace features
target_compile_definitions(perfetto_llm_native PRIVATE
    ANDROID_API_LEVEL=${ANDROID_PLATFORM_LEVEL}
)

# Include directories
target_include_directories(hnsw_native PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
)

target_include_directories(perfetto_llm_native PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${PERFETTO_SDK_PATH}/include
    "${NODE_MODULES_DIR}/react-native/ReactCommon/jsi"
)