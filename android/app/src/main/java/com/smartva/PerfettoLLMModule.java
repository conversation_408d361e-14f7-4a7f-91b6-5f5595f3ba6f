package com.smartva;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Promise;

public class PerfettoLLMModule extends ReactContextBaseJavaModule {
    
    private static boolean libraryLoaded = false;
    private static boolean bindingsInstalled = false;
    
    static {
        try {
            System.loadLibrary("perfetto_llm_native");
            libraryLoaded = true;
            System.out.println("✅ perfetto_llm_native library loaded successfully");
        } catch (UnsatisfiedLinkError e) {
            System.err.println("❌ Failed to load perfetto_llm_native library: " + e.getMessage());
            libraryLoaded = false;
        }
    }

    private native void installJSIBindings(long jsiPtr);

    public PerfettoLLMModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return "PerfettoLLMModule";
    }

    @ReactMethod
    public void installBindingsManually(Promise promise) {
        if (!libraryLoaded) {
            promise.reject("LIBRARY_NOT_LOADED", "Native library not loaded");
            return;
        }
        
        if (bindingsInstalled) {
            promise.resolve(true);
            return;
        }

        try {
            ReactApplicationContext context = getReactApplicationContext();
            if (context == null) {
                promise.reject("NO_CONTEXT", "React context is null");
                return;
            }

            if (!context.hasActiveReactInstance()) {
                promise.reject("NO_REACT_INSTANCE", "No active React instance");
                return;
            }

            long jsContextNativePointer = context.getJavaScriptContextHolder().get();
            if (jsContextNativePointer == 0) {
                promise.reject("INVALID_JS_CONTEXT", "Invalid JavaScript context pointer");
                return;
            }

            installJSIBindings(jsContextNativePointer);
            bindingsInstalled = true;
            System.out.println("✅ PerfettoLLMModule JSI bindings installed manually");
            promise.resolve(true);

        } catch (Exception e) {
            System.err.println("❌ Error installing JSI bindings: " + e.getMessage());
            e.printStackTrace();
            promise.reject("INSTALL_ERROR", e.getMessage());
        }
    }

    @ReactMethod
    public void checkBindingsStatus(Promise promise) {
        try {
            // Return status information
            promise.resolve(bindingsInstalled);
        } catch (Exception e) {
            promise.reject("STATUS_ERROR", e.getMessage());
        }
    }
}