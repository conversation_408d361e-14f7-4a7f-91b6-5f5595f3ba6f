const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');
const path = require('path');

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('@react-native/metro-config').MetroConfig}
 */
const config = {
  resolver: {
    assetExts: getDefaultConfig(__dirname).resolver.assetExts.concat([
      'onnx',
      'bin',
      'json'
    ]),
    unstable_enablePackageExports: true,
    unstable_conditionNames: []
  },
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: true,
        inlineRequires: true,
      },
    }),
  }
};



module.exports = mergeConfig(getDefaultConfig(__dirname), config);