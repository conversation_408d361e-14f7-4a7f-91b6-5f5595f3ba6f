// App.tsx - Simplified and Clean
import React, { useState, useRef, useEffect } from "react";
import {
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  View,
  Alert,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from "react-native";
import Icon from 'react-native-vector-icons/FontAwesome';

import { initLlama, releaseAllLlama } from "llama.rn";
import { downloadModel } from "./src/api/model";
import ProgressBar from "./src/components/ProgressBar";
import RNFS from "react-native-fs";
import axios from 'axios/dist/browser/axios.cjs';

// Import our modular components and services
import RAGService from './src/services/RAGService';
import PerfettoLLMTracer from './src/services/PerfettoLLMTracer';
import ConversationPage from './src/components/ConversationPage';
import ModelSelectionPage from './src/components/ModelSelectionPage';
import {
  HF_TO_GGUF,
  getOptimalChunkCount,
} from './src/config/ModelConfig';
import RAGUtils from './src/utils/RAGUtils';
import SystemMonitor from './src/components/SystemMonitor';

type Message = {
  role: "user" | "assistant" | "system";
  content: string;
  thought?: string;
  showThought?: boolean;
  context?: string;
  showContext?: boolean;
  retrievedDocs?: any[];
  queryTime?: number;
  cacheHits?: number;
  totalChunks?: number;
};

const INITIAL_CONVERSATION: Message[] = [
  {
    role: "system",
    content: "This is a conversation between user and assistant, a friendly chatbot. The assistant has access to a knowledge base and will use it to provide accurate information when available.",
  },
];

function App(): React.JSX.Element {
  // Core app state
  const [context, setContext] = useState<any>(null);
  const [conversation, setConversation] = useState<Message[]>(INITIAL_CONVERSATION);
  const [userInput, setUserInput] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true);

  // Model management state
  const [selectedModelFormat, setSelectedModelFormat] = useState<string>("");
  const [selectedGGUF, setSelectedGGUF] = useState<string | null>(null);
  const [availableGGUFs, setAvailableGGUFs] = useState<string[]>([]);
  const [downloadedModels, setDownloadedModels] = useState<string[]>([]);
  const [progress, setProgress] = useState<number>(0);
  const [isDownloading, setIsDownloading] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);

  // Performance tracking
  const [tokensPerSecond, setTokensPerSecond] = useState<Array<{
    tokensPerSecond: number;
    ttft: number;
    ragTime: number;
    totalTokens: number;
    prefillMetrics?: {
      tokensPerSecond: number;
      duration: number;
      tokenCount: number;
      cpuDelta?: number;
      memoryDelta?: number;
    };
    decodeMetrics?: {
      tokensPerSecond: number;
      duration: number;
      tokenCount: number;
      cpuDelta?: number;
      memoryDelta?: number;
    };
  } | number>>([]);

  // Navigation state
  const [currentPage, setCurrentPage] = useState<"modelSelection" | "conversation" | "systemMonitor">("modelSelection");

  // RAG state
  const [ragEnabled, setRagEnabled] = useState<boolean>(true);
  const [isLoadingRAG, setIsLoadingRAG] = useState<boolean>(false);
  const [ragStats, setRagStats] = useState(RAGService.getStats());
  const [customDocCount, setCustomDocCount] = useState<number | null>(null);

  // Document manager state
  const [showDocumentManager, setShowDocumentManager] = useState<boolean>(false);

  // Refs
  const scrollViewRef = useRef<ScrollView>(null);
  const scrollPositionRef = useRef(0);
  const contentHeightRef = useRef(0);

  // Initialize Perfetto on app start
  useEffect(() => {
    const initializePerfetto = async () => {
      console.log('🔧 Testing Perfetto foundation...');
      
      // Wait for React Native to be fully ready
      setTimeout(async () => {
        try {
          const status = PerfettoLLMTracer.getStatus();
          console.log('📊 Initial Perfetto status:', status);
          
          const initialized = await PerfettoLLMTracer.initialize();
          console.log('✅ Perfetto initialized:', initialized);
          
          if (initialized) {
            const finalStatus = PerfettoLLMTracer.getStatus();
            console.log('📊 Final Perfetto status:', finalStatus);
            
            // Quick test
            const traceStarted = await PerfettoLLMTracer.startTrace(['llm', 'test']);
            console.log('✅ Trace started:', traceStarted);
            
            if (traceStarted) {
              PerfettoLLMTracer.traceInferenceStart('test_model', 512);
              PerfettoLLMTracer.tracePrefillStart(10);
              PerfettoLLMTracer.tracePrefillEnd();
              PerfettoLLMTracer.traceInferenceEnd();
              
              setTimeout(async () => {
                const traceStopped = await PerfettoLLMTracer.stopTrace();
                console.log('✅ Trace stopped:', traceStopped);
              }, 1000);
            }
          }
        } catch (error) {
          console.error('❌ Error testing Perfetto:', error);
        }
      }, 2000);
    };
    
    initializePerfetto();
  }, []);

  // Initialize app
  useEffect(() => {
    initializeApp();
    return () => {
      if (context) {
        releaseAllLlama();
      }
      RAGService.clearCaches();
    };
  }, []);

  // Update RAG stats periodically
  useEffect(() => {
    const interval = setInterval(() => {
      setRagStats(RAGService.getStats());
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  // Check downloaded models when page changes
  useEffect(() => {
    if (currentPage === "modelSelection") {
      checkDownloadedModels();
    }
  }, [currentPage]);

  const initializeApp = async () => {
    await checkDownloadedModels();

    if (ragEnabled) {
      await initializeRAG();
    }
  };

  const initializeRAG = async () => {
    setIsLoadingRAG(true);
    try {
      console.log("🚀 Initializing RAG system...");
      const success = await RAGService.initialize();
      
      if (success) {
        console.log("✅ RAG system initialized successfully");
        setRagStats(RAGService.getStats());
        
        // Test the system
        setTimeout(async () => {
          try {
            await testRAGSystem();
          } catch (error) {
            console.warn("RAG test failed:", error);
          }
        }, 1000);
      } else {
        throw new Error("RAG initialization returned false");
      }
    } catch (error) {
      console.error("❌ RAG initialization failed:", error);
      Alert.alert(
        "RAG System Error",
        `Failed to initialize RAG system: ${(error as Error).message}\n\nYou can still use the chat without RAG features.`,
        [
          { text: "Disable RAG", onPress: () => setRagEnabled(false), style: "destructive" },
          { text: "Keep Trying", style: "default" }
        ]
      );
    } finally {
      setIsLoadingRAG(false);
    }
  };

  const testRAGSystem = async () => {
    try {
      console.log("🧪 Testing RAG system...");
      const testQuery = "test query";
      const results = await RAGService.querySimilarDocuments(testQuery, 3);
      console.log(`✅ Search test completed: ${results.documents.length} docs retrieved`);
    } catch (error) {
      console.warn("⚠️ RAG test failed:", error);
    }
  };

  const checkDownloadedModels = async () => {
    try {
      const files = await RNFS.readDir(RNFS.DocumentDirectoryPath);
      const ggufFiles = files
        .filter((file) => file.name.endsWith(".gguf"))
        .map((file) => file.name);
      setDownloadedModels(ggufFiles);
    } catch (error) {
      console.error("Error checking downloaded models:", error);
    }
  };

  const handleFormatSelection = (format: string) => {
    setSelectedModelFormat(format);
    setAvailableGGUFs([]);
    fetchAvailableGGUFs(format);
  };

  const fetchAvailableGGUFs = async (modelFormat: string) => {
    setIsFetching(true);
    try {
      const repoPath = HF_TO_GGUF[modelFormat as keyof typeof HF_TO_GGUF];
      const response = await axios.get(`https://huggingface.co/api/models/${repoPath}`);
      const files = response.data.siblings.filter((file: any) =>
        file.rfilename.endsWith(".gguf")
      );
      setAvailableGGUFs(files.map((file: any) => file.rfilename));
    } catch (error) {
      Alert.alert("Error", "Failed to fetch .gguf files from Hugging Face API.");
    } finally {
      setIsFetching(false);
    }
  };

  const handleGGUFSelection = (file: string) => {
    setSelectedGGUF(file);
    Alert.alert(
      "Confirm Download",
      `Do you want to download ${file}?`,
      [
        { text: "No", onPress: () => setSelectedGGUF(null), style: "cancel" },
        { text: "Yes", onPress: () => handleDownloadAndNavigate(file) },
      ]
    );
  };

  const handleLoadModel = (file: string) => {
    loadModel(file);
    setCurrentPage("conversation");
    setSelectedGGUF(file);
  };

  const handleDownloadAndNavigate = async (file: string) => {
    await handleDownloadModel(file);
    setCurrentPage("conversation");
  };

  const handleDownloadModel = async (file: string) => {
    const downloadUrl = `https://huggingface.co/${HF_TO_GGUF[selectedModelFormat as keyof typeof HF_TO_GGUF]}/resolve/main/${file}`;
    setIsDownloading(true);
    setProgress(0);

    const destPath = `${RNFS.DocumentDirectoryPath}/${file}`;
    
    if (await RNFS.exists(destPath)) {
      const success = await loadModel(file);
      if (success) {
        Alert.alert("Info", `File ${file} already exists, loading it directly.`);
        setIsDownloading(false);
        return;
      }
    }

    try {
      await downloadModel(file, downloadUrl, (progress) => setProgress(progress));
      Alert.alert("Success", `Model downloaded successfully`);
      await loadModel(file);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      Alert.alert("Error", `Download failed: ${errorMessage}`);
    } finally {
      setIsDownloading(false);
    }
  };

  const loadModel = async (modelName: string) => {
    try {
      const destPath = `${RNFS.DocumentDirectoryPath}/${modelName}`;

      if (context) {
        await releaseAllLlama();
        setContext(null);
        setConversation(INITIAL_CONVERSATION);
      }

      const llamaContext = await initLlama({
        model: destPath,
        use_mlock: true,
        n_ctx: 2048,
        n_gpu_layers: 1,
      });

      setContext(llamaContext);
      Alert.alert("Model Loaded", "The model was successfully loaded.");
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      Alert.alert("Error Loading Model", errorMessage);
      return false;
    }
  };

  const handleBackToModelSelection = () => {
    setContext(null);
    releaseAllLlama();
    setConversation(INITIAL_CONVERSATION);
    setSelectedGGUF(null);
    setTokensPerSecond([]);
    setCustomDocCount(null);
    setCurrentPage("modelSelection");
  };

  const toggleRAG = () => {
    setRagEnabled(previous => !previous);
    if (!ragEnabled && !RAGService.getStats().isInitialized) {
      setTimeout(initializeRAG, 100);
    }
  };

  const toggleContext = (messageIndex: number) => {
    setConversation((prev) =>
      prev.map((msg, index) =>
        index === messageIndex ? { ...msg, showContext: !msg.showContext } : msg
      )
    );
  };

  const toggleThought = (messageIndex: number) => {
    setConversation((prev) =>
      prev.map((msg, index) =>
        index === messageIndex ? { ...msg, showThought: !msg.showThought } : msg
      )
    );
  };

  const handleSendMessage = async () => {
    if (!context) {
      Alert.alert("Model Not Loaded", "Please load the model first.");
      return;
    }
    if (!userInput.trim()) {
      Alert.alert("Input Error", "Please enter a message.");
      return;
    }

    const newConversation: Message[] = [
      ...conversation,
      { role: "user", content: userInput },
    ];
    setConversation(newConversation);
    setUserInput("");
    setIsLoading(true);
    setIsGenerating(true);
    setAutoScrollEnabled(true);

    try {
      // **PERFETTO: Start comprehensive inference tracing**
      const modelName = selectedGGUF || 'unknown_model';
      const contextLength = newConversation.length;
      
      PerfettoLLMTracer.traceInferenceStart(modelName, contextLength);

      // Enhanced RAG retrieval
      let retrievedContext = "";
      let retrievedDocs = [];
      let ragTiming = null;

      if (ragEnabled && RAGService.getStats().isInitialized) {
        try {
          console.log("🔍 Starting RAG retrieval...");

          const baseChunkCount = getOptimalChunkCount(selectedGGUF);
          const adaptiveChunkCount = customDocCount || RAGUtils.getAdaptiveChunkCount(userInput, baseChunkCount);

          const ragResults = await RAGService.querySimilarDocuments(userInput, adaptiveChunkCount);
          retrievedDocs = ragResults.documents;

          ragTiming = {
            queryTime: ragResults.queryTime,
            cacheHits: ragResults.cacheStats.hits,
            totalChunks: ragResults.cacheStats.hits + ragResults.cacheStats.misses
          };

          if (retrievedDocs.length > 0) {
            const rankedDocs = RAGUtils.rankDocumentsByRelevance(userInput, retrievedDocs);
            retrievedDocs = rankedDocs;
            retrievedContext = RAGUtils.formatRetrievedContext(retrievedDocs);
            console.log(`✅ RAG retrieval: ${retrievedDocs.length} docs in ${(ragTiming.queryTime / 1000).toFixed(2)}s`);
            setTimeout(() => RAGUtils.smartPrefetch(newConversation), 1000);
          } else {
            console.log("⚠️ RAG retrieval returned no relevant documents");
          }

        } catch (ragError) {
          console.error("❌ RAG retrieval failed:", ragError);
          retrievedContext = "";
          retrievedDocs = [];
        }
      }

      const stopWords = [
        "</s>", "<|end|>", "user:", "assistant:", "<|im_end|>", 
        "<|eot_id|>", "<|end▁of▁sentence|>", "<|end_of_text|>", 
        "<｜end▁of▁sentence｜>"
      ];
      
      let chat = [...newConversation];

      if (retrievedContext && ragEnabled) {
        const enhancedSystemMessage = RAGUtils.createEnhancedSystemMessage(
          conversation[0].content, 
          retrievedContext
        );
        chat[0] = enhancedSystemMessage;
      }

      setConversation((prev) => [
        ...prev,
        {
          role: "assistant",
          content: "",
          thought: undefined,
          showThought: false,
          context: retrievedContext,
          showContext: false,
          retrievedDocs: retrievedDocs,
          queryTime: ragTiming?.queryTime,
          cacheHits: ragTiming?.cacheHits,
          totalChunks: ragTiming?.totalChunks,
        },
      ]);

      // **PERFETTO: Enhanced inference with detailed tracing**
      let currentAssistantMessage = "";
      let currentThought = "";
      let inThinkBlock = false;
      let firstTokenTime: number | null = null;
      let tokenCount = 0;
      let prefillComplete = false;
      const generationStartTime = Date.now();

      interface CompletionData {
        token: string;
      }

      interface CompletionResult {
        timings: {
          prompt_n: number;
          prompt_ms: number;
          prompt_per_token_ms: number;
          prompt_per_second: number;
          predicted_n: number;
          predicted_ms: number;
          predicted_per_token_ms: number;
          predicted_per_second: number;
        };
      }

      // **PERFETTO: Start prefill phase tracing**
      const inputTokenCount = chat.reduce((sum, msg) => sum + (msg.content?.length || 0) / 4, 0);
      PerfettoLLMTracer.tracePrefillStart(Math.round(inputTokenCount));

      const result: CompletionResult = await context.completion(
        {
          messages: chat,
          n_predict: 10000,
          stop: stopWords,
        },
        async (data: CompletionData) => {
          const token = data.token;
          tokenCount++;

          // **PERFETTO: Detect prefill completion (first token)**
          if (firstTokenTime === null) {
            firstTokenTime = Date.now();
            
            // **PERFETTO: End prefill phase, start decode phase**
            PerfettoLLMTracer.tracePrefillEnd();
            PerfettoLLMTracer.traceDecodeStart();
            prefillComplete = true;
          }

          // **PERFETTO: Trace token generation (sample every 10th token)**
          if (tokenCount % 10 === 0) {
            PerfettoLLMTracer.traceTokenGenerated(token, tokenCount);
          }

          currentAssistantMessage += token;

          if (token.includes("<think>")) {
            inThinkBlock = true;
            currentThought = token.replace("<think>", "");
          } else if (token.includes("</think>")) {
            inThinkBlock = false;
            const finalThought = currentThought.replace("</think>", "").trim();

            setConversation((prev) => {
              const lastIndex = prev.length - 1;
              const updated = [...prev];
              updated[lastIndex] = {
                ...updated[lastIndex],
                content: updated[lastIndex].content.replace(
                  `<think>${finalThought}</think>`,
                  ""
                ),
                thought: finalThought,
              };
              return updated;
            });

            currentThought = "";
          } else if (inThinkBlock) {
            currentThought += token;
          }

          const visibleContent = currentAssistantMessage
            .replace(/<think>.*?<\/think>/g, "")
            .trim();

          setConversation((prev) => {
            const lastIndex = prev.length - 1;
            const updated = [...prev];
            updated[lastIndex].content = visibleContent;
            return updated;
          });

          if (autoScrollEnabled && scrollViewRef.current) {
            requestAnimationFrame(() => {
              scrollViewRef.current?.scrollToEnd({ animated: false });
            });
          }
        }
      );

      // **PERFETTO: End decode phase**
      if (prefillComplete) {
        PerfettoLLMTracer.traceDecodeEnd();
      }

      // **PERFETTO: End overall inference**
      PerfettoLLMTracer.traceInferenceEnd();

      // Calculate performance metrics
      const totalTime = Date.now() - generationStartTime;
      const ttft = firstTokenTime ? (firstTokenTime - generationStartTime) / 1000 : 0;
      const actualTokensPerSecond = tokenCount > 0 ? tokenCount / (totalTime / 1000) : 0;

      const timings = result.timings;
      const prefillMetrics = timings ? {
        tokensPerSecond: timings.prompt_per_second || 0,
        duration: timings.prompt_ms || 0,
        tokenCount: timings.prompt_n || 0,
      } : undefined;
      
      const decodeMetrics = timings ? {
        tokensPerSecond: timings.predicted_per_second || 0,
        duration: timings.predicted_ms || 0,
        tokenCount: timings.predicted_n || 0,
      } : undefined;

      // **PERFETTO: Enhanced performance logging with trace correlation**
      console.log('🔍 Enhanced Inference Performance (Perfetto Traced):', {
        tokensPerSecond: actualTokensPerSecond,
        ttft: ttft,
        totalTime: totalTime,
        totalTokens: tokenCount,
        prefill: {
          duration: `${timings?.prompt_ms || 0}ms`,
          tokensPerSecond: timings?.prompt_per_second || 0,
          tokenCount: timings?.prompt_n || 0
        },
        decode: {
          duration: `${timings?.predicted_ms || 0}ms`,
          tokensPerSecond: timings?.predicted_per_second || 0,
          tokenCount: timings?.predicted_n || 0
        },
        perfettoTraceActive: PerfettoLLMTracer.isTracing()
      });

      setTokensPerSecond((prev) => [
        ...prev,
        {
          tokensPerSecond: parseFloat(actualTokensPerSecond.toFixed(2)),
          ttft: parseFloat(ttft.toFixed(2)),
          ragTime: ragTiming ? parseFloat((ragTiming.queryTime / 1000).toFixed(2)) : 0,
          totalTokens: tokenCount,
          prefillMetrics,
          decodeMetrics
        }
      ]);

      setRagStats(RAGService.getStats());

    } catch (error) {
      // **PERFETTO: Trace inference error**
      PerfettoLLMTracer.traceInferenceEnd();
      
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      Alert.alert("Error During Inference", errorMessage);
    } finally {
      setIsLoading(false);
      setIsGenerating(false);
    }
  };

  const stopGeneration = async () => {
    try {
      await context.stopCompletion();
      setIsGenerating(false);
      setIsLoading(false);

      setConversation((prev) => {
        const lastMessage = prev[prev.length - 1];
        if (lastMessage.role === "assistant") {
          return [
            ...prev.slice(0, -1),
            {
              ...lastMessage,
              content: lastMessage.content + "\n\n*Generation stopped by user*",
            },
          ];
        }
        return prev;
      });
    } catch (error) {
      console.error("Error stopping completion:", error);
    }
  };

  const handleScroll = (event: any) => {
    const currentPosition = event.nativeEvent.contentOffset.y;
    const contentHeight = event.nativeEvent.contentSize.height;
    const scrollViewHeight = event.nativeEvent.layoutMeasurement.height;

    scrollPositionRef.current = currentPosition;
    contentHeightRef.current = contentHeight;

    const distanceFromBottom = contentHeight - scrollViewHeight - currentPosition;
    setAutoScrollEnabled(distanceFromBottom < 100);
  };

  const runDiagnostics = async () => {
    try {
      const diagnostics = await RAGService.runDiagnostics();
      
      const failedTests = Object.values(diagnostics.results).filter(r => 
        typeof r === 'string' && r.includes('FAILED')
      ).length;
      const totalTests = Object.keys(diagnostics.results).length;
      
      Alert.alert(
        "RAG Diagnostics",
        `${totalTests - failedTests}/${totalTests} tests passed\n\nCheck console for detailed results.`,
        [{ text: "OK" }]
      );
    } catch (error) {
      Alert.alert("Diagnostics Error", (error as Error).message);
    }
  };

  const clearCaches = () => {
    RAGService.clearCaches();
    setRagStats(RAGService.getStats());
    Alert.alert("Cache Cleared", "All RAG caches have been cleared.");
  };

  const handleDocCountChange = (count: number | null) => {
    setCustomDocCount(count);
    if (count === null) {
      console.log(`📊 Document count reset to auto mode`);
    } else {
      console.log(`📊 Custom document count set to: ${count}`);
    }
  };

  const clearChat = () => {
    setConversation(INITIAL_CONVERSATION);
    setTokensPerSecond([]);
    console.log('🧹 Chat cleared');
  };

  const testTerm = async (term: string) => {
    try {
      const result = await RAGService.testTermEmbedding(term);
      console.log(`🔍 Term test result for "${term}":`, result);
      Alert.alert(
        "Term Test Results",
        `Platform: ${result.platform}\nTokenizer: ${result.tokenizerType}\nSearch Results: ${result.searchResultCount} docs\nEmbedding Dims: ${result.embeddingDimensions}`,
        [{ text: "OK" }]
      );
    } catch (error) {
      console.error('❌ Term test failed:', error);
      Alert.alert("Test Failed", (error as Error).message);
    }
  };

  const handleDocumentAdded = () => {
    setRagStats(RAGService.getStats());
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <ScrollView
          style={styles.scrollView}
          ref={scrollViewRef}
          onScroll={handleScroll}
          scrollEventThrottle={16}
        >
          <Text style={styles.title}>{`SmartVA: 🔪\nEverything on Edge`}</Text>

          {/* Model Selection Page */}
          {currentPage === "modelSelection" && !isDownloading && (
            <ModelSelectionPage
              selectedModelFormat={selectedModelFormat}
              availableGGUFs={availableGGUFs}
              downloadedModels={downloadedModels}
              selectedGGUF={selectedGGUF}
              isFetching={isFetching}
              onFormatSelection={handleFormatSelection}
              onGGUFSelection={handleGGUFSelection}
              onLoadModel={handleLoadModel}
            />
          )}

          {/* Conversation Page */}
          {currentPage === "conversation" && !isDownloading && (
            <ConversationPage
              selectedGGUF={selectedGGUF}
              ragEnabled={ragEnabled}
              onToggleRAG={toggleRAG}
              ragStats={ragStats}
              customDocCount={customDocCount}
              conversation={conversation}
              tokensPerSecond={tokensPerSecond}
              onToggleContext={toggleContext}
              onToggleThought={toggleThought}
              userBubbleStyle={styles.userBubble}
              llamaBubbleStyle={styles.llamaBubble}
              onRunDiagnostics={runDiagnostics}
              onClearCaches={clearCaches}
              onDocCountChange={handleDocCountChange}
              onTestTerm={testTerm}
              showDocumentManager={showDocumentManager}
              setShowDocumentManager={setShowDocumentManager}
              onDocumentAdded={handleDocumentAdded}
            />
          )}

          {/* Download Progress */}
          {isDownloading && (
            <View style={styles.card}>
              <Text style={styles.subtitle}>Downloading: {selectedGGUF}</Text>
              <ProgressBar progress={progress} />
            </View>
          )}

          {/* System Monitor Page */}
          {currentPage === "systemMonitor" && (
            <SystemMonitor />
          )}

          {/* RAG Loading */}
          {isLoadingRAG && (
            <View style={styles.ragLoadingCard}>
              <Text style={styles.subtitle}>Initializing RAG system...</Text>
              <ActivityIndicator size="large" color="#2563EB" />
            </View>
          )}
        </ScrollView>

        {/* Bottom Input Area */}
        <View style={styles.bottomContainer}>
          {currentPage === "conversation" && (
            <>
              <View style={styles.inputContainer}>
                <View style={styles.inputRow}>
                  <TextInput
                    style={styles.input}
                    placeholder="Type your message..."
                    placeholderTextColor="#94A3B8"
                    value={userInput}
                    onChangeText={setUserInput}
                    multiline
                  />
                  {isGenerating ? (
                    <TouchableOpacity
                      style={styles.stopButton}
                      onPress={stopGeneration}
                    >
                      <Text style={styles.buttonText}>□ Stop</Text>
                    </TouchableOpacity>
                  ) : (
                    <TouchableOpacity
                      style={styles.sendButton}
                      onPress={handleSendMessage}
                      disabled={isLoading}
                    >
                      <Text style={styles.buttonText}>
                        {isLoading ? "Sending..." : "Send"}
                      </Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
              <View style={styles.inputContainer}>
                <View style={styles.inputRow}>
                  <TouchableOpacity
                   style={styles.backButton}
                   onPress={handleBackToModelSelection}
                  >
                   <Text style={styles.backButtonText}>
                    ← Back to Model Selection
                   </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                   style={styles.monitorButton}
                   onPress={() => setCurrentPage("systemMonitor")}
                  >
                   <Icon name="bar-chart" size={16} color="#FFFFFF" />
                   <Text style={styles.monitorButtonText}>Monitor</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                   style={styles.clearChatButton}
                   onPress={clearChat}
                  >
                   <Icon name="trash-o" size={20} color="#FFFFFF" />
                   <Text style={styles.clearChatButtonText}>Chat</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </>
          )}

          {/* System Monitor Navigation */}
          {currentPage === "systemMonitor" && (
            <View style={styles.inputContainer}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => setCurrentPage("conversation")}
              >
                <Text style={styles.backButtonText}>
                  ← Back to Chat
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  scrollView: {
    paddingBottom: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: "700",
    color: "#1E293B",
    marginVertical: 24,
    textAlign: "center",
  },
  card: {
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    padding: 24,
    margin: 16,
    shadowColor: "#475569",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  subtitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#334155",
    marginBottom: 16,
    marginTop: 16,
  },
  userBubble: {
    alignSelf: "flex-end",
    backgroundColor: "#3B82F6",
  },
  llamaBubble: {
    alignSelf: "flex-start",
    backgroundColor: "#FFFFFF",
    borderWidth: 1,
    borderColor: "#E2E8F0",
  },
  bottomContainer: {
    backgroundColor: "#FFFFFF",
    borderTopWidth: 1,
    borderTopColor: "#E2E8F0",
    paddingBottom: Platform.OS === "ios" ? 20 : 10,
  },
  inputContainer: {
    padding: 16,
    backgroundColor: "#FFFFFF",
  },
  inputRow: {
    flexDirection: "row",
    gap: 12,
  },
  input: {
    flex: 1,
    backgroundColor: "#FFFFFF",
    borderWidth: 1,
    borderColor: "#E2E8F0",
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: "#334155",
    minHeight: 50,
    maxHeight: 120,
  },
  sendButton: {
    backgroundColor: "#3B82F6",
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
    shadowColor: "#3B82F6",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 2,
    alignSelf: "stretch",
    justifyContent: "center",
  },
  stopButton: {
    backgroundColor: "#FF3B30",
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignSelf: "stretch",
    justifyContent: "center",
  },
  buttonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
    textAlign: "center",
  },
  backButton: {
    backgroundColor: "#3B82F6",
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: "center",
  },
  backButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  monitorButton: {
    backgroundColor: '#10B981',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    justifyContent: "center",
    alignSelf: "stretch",
    alignItems: 'center',
    flexDirection: 'row',
    gap: 4,
  },
  monitorButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  clearChatButton: {
    backgroundColor: '#EF4444',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    justifyContent: "center",
    alignSelf: "stretch",
    alignItems: 'center',
    flexDirection: 'row',
    gap: 4,
  },
  clearChatButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  ragLoadingCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    padding: 24,
    margin: 16,
    shadowColor: "#475569",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    alignItems: "center",
  },
});

export default App;