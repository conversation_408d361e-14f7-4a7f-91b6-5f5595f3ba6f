<svg width="440" height="515" viewBox="0 0 440 515" xmlns="http://www.w3.org/2000/svg">
  <style>
    text, tspan {
      font-family: "Times New Roman";
    }
  </style>
  <!-- Background -->
  <rect width="440" height="515" fill="#FFFFFF"/>

  <!-- Bi-directional Line -->
  <path d="M 40 20 L 80 20" stroke="#000000" stroke-width="3"/>
  <text x="85" y="25" text-anchor="left" font-size="20" font-weight="bold">Bi-directional Flow</text>

  <!-- Device Boundary -->
  <rect x="20" y="40" width="400" height="455" fill="#E8E8E8" stroke="#000000" stroke-width="3" rx="15"/>
  <text x="40" y="70" font-size="22" font-weight="bold">DEVICE</text>
  <!-- Application Boundary -->
  <rect x="40" y="90" width="360" height="308" fill="#FFFFFF" stroke="#000000" stroke-width="3" rx="15"/>
  <text x="60" y="120" font-size="22" font-weight="bold" fill="#000000">APPLICATION</text>
  
  <!-- Application Interface -->
  <rect x="60" y="140" width="320" height="40" fill="#88CCEE" fill-opacity="0.3" stroke="#46A6D6" stroke-width="2" rx="10"/>
  <text x="220" y="165" text-anchor="middle" font-size="20" font-weight="bold">Application Interface</text>
  
  <!-- Document Management Layer -->
  <rect x="60" y="210" width="100" height="63" fill="#332288" fill-opacity="0.3" stroke="#201071" stroke-width="2" rx="10"/>
  <text x="110" y="235" text-anchor="middle" font-size="20" font-weight="bold">Document</text>
  <text x="110" y="258" text-anchor="middle" font-size="20" font-weight="bold">Manager</text>
  
  <!-- RAG Processing Pipeline -->
  <rect x="175" y="210" width="110" height="88" fill="#44AA99" fill-opacity="0.3" stroke="#288878" stroke-width="2" rx="10"/>
  <text x="230" y="235" text-anchor="middle" font-size="20" font-weight="bold">RAG</text>
  <text x="230" y="258" text-anchor="middle" font-size="20" font-weight="bold">Processing</text>
  <text x="230" y="281" text-anchor="middle" font-size="20" font-weight="bold">Pipeline</text>

  <!-- LLM Inference Engine -->
  <rect x="300" y="210" width="80" height="63" fill="#882255" fill-opacity="0.3" stroke="#6D103E" stroke-width="2" rx="10"/>
  <text x="340" y="235" text-anchor="middle" font-size="20" font-weight="bold">LLM</text>
  <text x="340" y="258" text-anchor="middle" font-size="20" font-weight="bold">Engine</text>
  
  <!-- Native Bridge -->
  <rect x="60" y="328" width="225" height="40" fill="#AA4499" fill-opacity="0.3" stroke="#8C2A7C" stroke-width="2" rx="10"/>
  <text x="167" y="353" text-anchor="middle" font-size="20" font-weight="bold">Native Bridge</text>
  
  
  <!-- Storage Layer -->
  <rect x="145" y="425" width="150" height="40" fill="#ffd93d" fill-opacity="0.3" stroke="#f39c12" stroke-width="2" rx="10"/>
  <text x="220" y="450" text-anchor="middle" font-size="20" font-weight="bold">Local Storage</text>
  
  <!-- Main Data Flow -->
  <!-- Application to Local Storage -->
  <path d="M 220 398 L 220 425" stroke="#000000" stroke-width="3"/>

  <!-- Internal Flows -->
  <!-- Application to Document Manager -->
  <path d="M 110 180 L 110 210" stroke="#000000" stroke-width="3"/>
  <!-- Application to RAG Processing Pipeline -->
  <path d="M 230 180 L 230 210" stroke="#000000" stroke-width="3"/>
  <!-- Application to LLM Engine -->
  <path d="M 340 180 L 340 210" stroke="#000000" stroke-width="3"/>
  <!-- Document Manager to Native Bridge -->
  <path d="M 110 273 L 110 328" stroke="#000000" stroke-width="3"/>
  <!-- RAG Processing Pipeline to Native Bridge -->
  <path d="M 230 298 L 230 328" stroke="#000000" stroke-width="3"/>

</svg>