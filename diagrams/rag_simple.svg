<svg width="440" height="400" viewBox="0 0 440 400" xmlns="http://www.w3.org/2000/svg">
  <style>
    text, tspan {
      font-family: "Times New Roman";
    }
  </style>
  <!-- Background -->
  <rect width="440" height="400" fill="#FFFFFF"/>
  <!-- Stage 1: Query Input -->
  <rect x="70" y="20" width="300" height="40" fill="#44AA99" fill-opacity="0.3" stroke="#288878" stroke-width="2" rx="8"/>
  <text x="220" y="45" text-anchor="middle" font-size="18" font-weight="bold">Stage 1: Query Input  Preprocessing</text>
  <!-- Stage 2: Tokenization -->
  <rect x="120" y="100" width="200" height="40" fill="#44AA99" fill-opacity="0.45" stroke="#288878" stroke-width="2" rx="8"/>
  <text x="220" y="125" text-anchor="middle" font-size="18" font-weight="bold" fill="#1a1a1a">Stage 2: Tokenization</text>
  <!-- Stage 3: Embedding Generation -->
  <rect x="80" y="180" width="280" height="40" fill="#44AA99" fill-opacity="0.6" stroke="#288878" stroke-width="2" rx="8"/>
  <text x="220" y="205" text-anchor="middle" font-size="18" font-weight="bold" fill="#1a1a1a">Stage 3: Embedding Generation</text>
  <!-- Stage 4: Vector Search -->
  <rect x="80" y="260" width="280" height="40" fill="#44AA99" fill-opacity="0.75" stroke="#288878" stroke-width="2" rx="8"/>
  <text x="220" y="285" text-anchor="middle" font-size="18" font-weight="bold" fill="#1a1a1a">Stage 4: HNSW Vector Search</text>
  <!-- Stage 5: Document Retrieval -->
  <rect x="20" y="340" width="400" height="40" fill="#44AA99" fill-opacity="0.9" stroke="#288878" stroke-width="2" rx="8"/>
  <text x="220" y="365" text-anchor="middle" font-size="18" font-weight="bold">Stage 5: Document Loading  Context Formation</text>
  <!-- Arrows -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  <!-- Flow arrows between stages -->
  <path d="M 220 60 L 220 100" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 220 140 L 220 180" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 220 220 L 220 260" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 220 300 L 220 340" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
</svg>