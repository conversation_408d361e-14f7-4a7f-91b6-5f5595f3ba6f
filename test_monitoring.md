# Enhanced CPU and Memory Monitoring Test Guide

## What Was Fixed

### 1. **Process-Specific CPU Tracking**
- **Before**: Only tracked system-wide CPU usage
- **After**: Now tracks both system CPU and process-specific CPU usage
- **Why**: Your app's CPU usage was getting lost in system-wide averages

### 2. **Improved CPU Calculation**
- **Before**: Used static variables that could give inconsistent results
- **After**: Uses proper time-based deltas with instance variables
- **Why**: CPU usage requires time differences to calculate properly

### 3. **Continuous Monitoring During Inference**
- **Before**: Only measured at start/end of phases
- **After**: Continuous monitoring every 500ms during inference
- **Why**: Memory and CPU usage changes throughout the inference process

### 4. **Enhanced Memory Tracking**
- **Before**: Only tracked basic memory usage
- **After**: Tracks RSS (actual memory), VmSize (virtual memory), and system memory
- **Why**: Different memory metrics show different aspects of usage

### 5. **Better Timing**
- **Before**: Measurements too close together (no time for changes)
- **After**: Proper intervals between measurements
- **Why**: CPU usage needs time to accumulate meaningful differences

## How to Test

### Step 1: Build and Run
```bash
cd android && ./gradlew assembleDebug
npx react-native run-android
```

### Step 2: Navigate to System Monitor
1. Load a model in the app
2. Go to the chat screen
3. Click the "Monitor" button (green button with bar chart icon)
4. Click "Start Monitoring"

### Step 3: Test During Inference
1. Go back to chat (← Back to Chat)
2. Start Perfetto tracing (if you have the tracing control component)
3. Send a message to trigger inference
4. Go back to System Monitor to see the results

### Step 4: Check Logs
```bash
adb logcat | grep -E "(PerfettoLLMTracer|TRACE)"
```

## Expected Results

### CPU Usage
- **System CPU**: Should show overall system load (0-100%)
- **Process CPU**: Should show your app's specific CPU usage
- **During inference**: Process CPU should spike during prefill/decode phases

### Memory Usage
- **System Memory**: Total system memory usage
- **Process RSS**: Your app's actual physical memory usage
- **Process VmSize**: Your app's virtual memory size
- **During inference**: Should see increases during model loading and inference

### Continuous Monitoring
- Updates every 500ms during inference phases
- Captures the full inference lifecycle, not just start/end points
- Shows real-time changes in resource usage

## Troubleshooting

### If CPU is still showing 0:
1. Check that the monitoring thread is starting (look for "Started continuous monitoring thread" in logs)
2. Verify that inference is actually running (check for "TRACE: Prefill Start" messages)
3. Make sure you're looking at Process CPU, not just System CPU

### If Memory doesn't change much:
1. Try with a larger model or longer prompts
2. Check Process VmSize instead of just RSS
3. Memory changes might be gradual - look at the average over time

### If monitoring isn't working:
1. Check that JSI bindings are installed ("JSI bindings installed successfully")
2. Verify the native library loaded ("perfetto_llm_native library loaded successfully")
3. Make sure you started tracing before inference

## Key Improvements

1. **Real-time monitoring**: No more missing the actual resource usage during inference
2. **Process isolation**: See exactly what your app is using vs system-wide usage
3. **Better granularity**: 500ms intervals capture short-lived spikes
4. **Multiple memory metrics**: Different views of memory usage patterns
5. **Proper CPU calculation**: Time-based deltas give accurate percentages

The enhanced monitoring should now give you meaningful data about both CPU and memory usage during your LLM inference phases!
