4 System Architecture 

The system implements RAG entirely on-device, eliminating the need for cloud-based inference. The architecture, shown in [Figure 1](#fig1), leverages a modular design integrating cross-platform capabilities with high-performance native libraries. Key features of the application include:

- Adaptive document retrieval
- Add/remove documents from the RAG in the application
- Displayed performance metrics Tokens Per Second (TPS), Time To First Token (TTFT), RAG retrieval time, token count
- Isolated text generation and embedding runtime environments
- Efficient RAG vector search
- Offline usage

4.1 Architectural Overview

The architecture comprises six primary components: (1) application interface, (2) document manager, (3) RAG processing pipeline, (4) LLM engine, (5) native bridge, and (6) data processing pipeline (optional). The operational flow across these six architectural components proceeds as follows: 

![The system operates entirely on-device within a mobile application. The architecture comprises an (1) Application Interface that coordinates three core components: (2) Document Manager for user content ingestion, (3) RAG Processing Pipeline for semantic search and retrieval, and (4) LLM Engine for response generation. Both Document Manager and RAG Processing Pipeline components perform optimized computations through a (5) Native Bridge. An optional external (6) RAG Server provides supplementary data processing capabilities via internet connectivity. All feature of the system architecture either contribute to or utilize files on the File System Storage of the device.](diagrams/system_architecture_simple_RAGServer.svg){#fig1}


4.1.1 Application Interface
This component is composed of a unified mobile interface that is built using React Native. React Native was selected for app development due to its ability to enable cross-platform deployment from a single codebase, significantly reducing both development time and costs while ensuring a consistent user experience across iOS and Android devices. This open-source framework leverages native components for high performance and allows for rapid iteration and streamlined maintenance, making it an efficient and cost-effective solution for modern mobile application projects. GGUF is a binary format that is optimized for quick loading and saving of models, making it highly efficient for inference purposes. Users can dynamically download quantized GGUF models that are developed for mobile LLM inferencing, which are then maintained on the device's file system storage. Models are downloaded from Hugging Face, a platform where the machine learning community collaborates on models, datasets, and applications.

Through the application interface users can interact with the document manager to add or remove documents from the RAG. Fine-grained control of the RAG processing pipeline is achieved through various features of the application interface. The interface adaptively sets the default number of documents retrieved based on the context window size of the selected LLM. Users can enable or disable the RAG component using a toggle switch in the chat interface. One can manually select the number of documents to be retrieved from the "Debug Panel" dropdown menu in the chat interface. From the "Debug Panel" the user is also able to perform quick retrieval tests, run diagnostics, and see RAG statistics such as: how many documents are available in the RAG component, how many document chunks are cached, what is the hit rate(%) of retrieved documents already present in the cache for the most recent query, and memory usage of the document cache. With RAG assisted queries, the user can examine the retrieved documents that were added to the original query by toggling the "Show Retrieved Context" dropdown menu in the model's response. Metrics such as TPS, TTFT, RAG retrieval time, and token count are displayed for each response. The interface provides a responsive chat experience with streaming capabilities by coordinating queries and responses with the LLM via the LLM engine. Finally, device performance, namely memory and CPU(%) usage consumed by the selected LLM and embedding model, is displayed to the user within the chat interface.

The application interface allows for ease of experimentation by allowing the user to switch seamlessly between different LLMs and toggle the RAG component on or off at will. These features of the application interface culminate in the ability for the user to easily perform a comparative analysis of various models and configurations. 

4.1.2 Document Manager

The Document Manager serves as the primary interface for content ingestion and curation within the RAG system, providing users with intuitive controls for adding and removing documents from the knowledge base. As depicted in Figure 1, this component operates as a critical intermediary between the application interface and the file system storage, enabling dynamic management of the document corpus that feeds the RAG processing pipeline.

At the architectural level, the Document Manager implements a streamlined workflow that handles document lifecycle management through direct integration with the device's file system storage. When users add documents through the application interface, the Document Manager coordinates with the native bridge to ensure efficient storage and indexing operations. Similarly, document removal operations are executed through the same pathway, maintaining consistency between the user interface state and the underlying storage layer. This bidirectional communication ensures that document changes are immediately reflected in both the application interface and the file system storage, providing users with real-time feedback on their document management operations.

The component's design prioritizes user experience through seamless document handling capabilities that abstract away the underlying complexity of file system operations. Users can add documents to the RAG through simple interface interactions, with the Document Manager handling format validation, storage allocation, and metadata management transparently. Document removal operations are similarly streamlined, allowing users to selectively remove documents from the knowledge base with immediate effect on subsequent RAG queries. The component maintains a consistent view of the document corpus across the application interface, ensuring that users have accurate information about which documents are currently available for retrieval.

From a technical perspective, the Document Manager implements robust error handling and validation mechanisms to ensure data integrity throughout the document management process. The component validates document formats, manages storage space efficiently, and provides feedback to users regarding the success or failure of document operations. Integration with the native bridge enables optimized file system operations that maintain performance even with large document collections, while the connection to the RAG processing pipeline ensures that newly added documents are immediately available for semantic search and retrieval operations.

4.1.3 RAG Processing Pipeline
The RAG processing component serves as the intelligent coordinator between user queries and document retrieval, implementing adaptive strategies that optimize performance across different model configurations. Key architectural decisions of the RAG processing pipeline include separating embedding operations from vector search to enable concurrent processing, implementing multi-tiered caching to minimize repeated computations, and developing query complexity analysis to dynamically adjust retrieval parameters. The pipelines's modular design allows independent optimization of tokenization, embedding, and ranking components while maintaining consistent interfaces with both the LLM inference layer and native search operations.

![Figure 2. RAG Processing Pipeline Technical Detail. The pipeline processes user queries through five distinct stages: (1) adaptive preprocessing that determines optimal document retrieval count based on query complexity and the selected model's context window size, (2) dual-tokenizer strategy with local BERT vocabulary or bundled fallback, (3) quantized ONNX embedding generation with attention-weighted pooling, (4) native Rust HNSW vector search with memory-mapped indices achieving sub-5ms performance, and (5) efficient document loading with LRU caching and relevance ranking for context formation.](diagrams/rag_simple.svg)

4.1.4 LLM Inference Engine
The LLM inference engine operates independently from the RAG processing pipeline, enabling parallel model loading and execution (See Figure 5). This separation allows the system to maintain LLM state while dynamically incorporating retrieved contexts, supporting hot-swapping between different quantized models without disrupting the embedding pipeline. The architectural isolation ensures memory-intensive LLM operations do not interfere with the lightweight embedding and search processes, though performance degrades as context length increases with additional retrieved documents.

4.1.5 Native Bridge

The native bridge subsytem addresses the fundamental challenge of deploying high-performance vector search on resource-constrained mobile devices. The architectural approach prioritizes write-once, deploy-everywhere functionality through a unified Rust core with platform-specific integration layers. This design enables consistent search performance across iOS and Android while accommodating each platform's distinct native integration requirements. The offline index construction strategy allows for aggressive optimization during the build phase, trading increased preparation time for superior runtime performance on mobile hardware.

![Figure 3. Native Bridge Architecture for Cross-Platform Rust Integration. The system employs a unified React Native bridge interface that routes to platform-specific implementations: iOS uses direct Objective-C bindings with C header integration (HnswSearch.h) linking to a static Rust library (libhnsw_lib.a), while Android requires an additional JNI layer (hnsw_jni.cpp) compiled into a shared object (libhnsw_native.so) before accessing the same static Rust core. Both platforms converge on identical Rust FFI functions (hnsw_search()) ensuring consistent cross-platform behavior while accommodating platform-specific integration requirements. The architecture demonstrates how a single Rust codebase can provide high-performance native functionality across mobile platforms through appropriate abstraction layers.](diagrams/native_bridge.svg)

4.6 Data Processing Pipeline

The Python-based offline pipeline prepares datasets from PDFs, Excel files, and plain texts, employing intelligent extraction and semantic-aware chunking strategies. Using identical ONNX embedding models for consistency with runtime operations, the pipeline balances chunk context and retrieval granularity (200-500 tokens per chunk), preserving semantic coherence through chunk overlap. 

![Figure 4. Document Processing and Storage Architecture. The system employs a three-phase approach: (1) offline processing through embed_docs.py that ingests multiple document formats, applies intelligent sentence-aware chunking (1000 characters with 200-character overlap), generates 384-dimensional embeddings using MiniLM-L6-v2, and builds HNSW indices via custom Rust CLI with optimized parameters (M=16, ef\_construction=200); (2) storage architecture featuring chunked metadata system with 1000 documents per 5MB file for mobile memory constraints, memory-mapped hnsw index files (.data/.graph), and automated cross-platform asset distribution; and (3) runtime memory management using LRU cache system (8-chunk maximum, ~40MB) with intelligent O(1) document loading via floor(docID/1000) formula, achieving 85% cache hit rates and 2-5ms load times with automatic prefetching of initial chunks.](diagrams/data_prep_pipeline.svg)