import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';
import PerfettoLLMTracer from '../services/PerfettoLLMTracer';

const PerfettoTracingControl: React.FC = () => {
  const [tracingStatus, setTracingStatus] = useState(PerfettoLLMTracer.getStatus());

  const updateStatus = () => {
    setTracingStatus(PerfettoLLMTracer.getStatus());
  };

  const startTrace = async () => {
    const success = await PerfettoLLMTracer.startTrace(['llm', 'inference', 'system']);
    updateStatus();
    Alert.alert(
      'Perfetto Tracing', 
      success ? 'Tracing started successfully' : 'Failed to start tracing'
    );
  };

  const stopTrace = async () => {
    const success = await PerfettoLLMTracer.stopTrace();
    updateStatus();
    Alert.alert(
      'Perfetto Tracing',
      success ? 
        'Tracing stopped. Check logs with:\nadb logcat | grep -E "(TRACE|PerfettoLLMTracer)"' : 
        'Failed to stop tracing'
    );
  };

  return (
    <View style={styles.tracingControl}>
      <Text style={styles.tracingTitle}>📊 Perfetto Tracing</Text>
      <Text style={styles.tracingStatus}>
        Status: {tracingStatus.isTracing ? '🔴 ACTIVE' : '⚪ IDLE'}
      </Text>
      
      <View style={styles.tracingButtons}>
        <TouchableOpacity
          style={[styles.tracingButton, styles.startButton]}
          onPress={startTrace}
          disabled={tracingStatus.isTracing}
        >
          <Text style={styles.tracingButtonText}>Start</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tracingButton, styles.stopButton]}
          onPress={stopTrace}
          disabled={!tracingStatus.isTracing}
        >
          <Text style={styles.tracingButtonText}>Stop</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  tracingControl: {
    backgroundColor: '#F8FAFC',
    borderRadius: 12,
    padding: 12,
    margin: 8,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  tracingTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#334155',
    marginBottom: 4,
  },
  tracingStatus: {
    fontSize: 12,
    color: '#64748B',
    marginBottom: 8,
  },
  tracingButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  tracingButton: {
    flex: 1,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  startButton: {
    backgroundColor: '#10B981',
  },
  stopButton: {
    backgroundColor: '#EF4444',
  },
  tracingButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
});

export default PerfettoTracingControl;