import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import PerfettoLLMTracer from '../services/PerfettoLLMTracer';

interface SystemStats {
  systemCpu: number;
  processCpu: number;
  systemMemory: number;
  processMemory: number;
  timestamp: number;
}

const SystemMonitor: React.FC = () => {
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [history, setHistory] = useState<SystemStats[]>([]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isMonitoring) {
      interval = setInterval(() => {
        const newStats = PerfettoLLMTracer.getSystemStats();
        setStats(newStats);
        
        // Keep last 10 measurements
        setHistory(prev => {
          const updated = [...prev, newStats];
          return updated.slice(-10);
        });
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isMonitoring]);

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatCpu = (cpu: number) => {
    return `${cpu.toFixed(1)}%`;
  };

  const getAverageStats = () => {
    if (history.length === 0) return null;
    
    const avg = history.reduce((acc, stat) => ({
      systemCpu: acc.systemCpu + stat.systemCpu,
      processCpu: acc.processCpu + stat.processCpu,
      systemMemory: acc.systemMemory + stat.systemMemory,
      processMemory: acc.processMemory + stat.processMemory,
    }), { systemCpu: 0, processCpu: 0, systemMemory: 0, processMemory: 0 });

    const count = history.length;
    return {
      systemCpu: avg.systemCpu / count,
      processCpu: avg.processCpu / count,
      systemMemory: avg.systemMemory / count,
      processMemory: avg.processMemory / count,
    };
  };

  const averageStats = getAverageStats();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>📊 System Monitor</Text>
      
      <TouchableOpacity
        style={[styles.button, isMonitoring ? styles.stopButton : styles.startButton]}
        onPress={() => setIsMonitoring(!isMonitoring)}
      >
        <Text style={styles.buttonText}>
          {isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}
        </Text>
      </TouchableOpacity>

      {stats && (
        <ScrollView style={styles.statsContainer}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Current Stats</Text>
            <Text style={styles.stat}>System CPU: {formatCpu(stats.systemCpu)}</Text>
            <Text style={styles.stat}>Process CPU: {formatCpu(stats.processCpu)}</Text>
            <Text style={styles.stat}>System Memory: {formatBytes(stats.systemMemory)}</Text>
            <Text style={styles.stat}>Process Memory: {formatBytes(stats.processMemory)}</Text>
          </View>

          {averageStats && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Average (Last {history.length} samples)</Text>
              <Text style={styles.stat}>System CPU: {formatCpu(averageStats.systemCpu)}</Text>
              <Text style={styles.stat}>Process CPU: {formatCpu(averageStats.processCpu)}</Text>
              <Text style={styles.stat}>System Memory: {formatBytes(averageStats.systemMemory)}</Text>
              <Text style={styles.stat}>Process Memory: {formatBytes(averageStats.processMemory)}</Text>
            </View>
          )}

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Recent History</Text>
            {history.slice(-5).reverse().map((stat, index) => (
              <View key={stat.timestamp} style={styles.historyItem}>
                <Text style={styles.historyText}>
                  {new Date(stat.timestamp).toLocaleTimeString()}: 
                  CPU {formatCpu(stat.processCpu)}, 
                  MEM {formatBytes(stat.processMemory)}
                </Text>
              </View>
            ))}
          </View>
        </ScrollView>
      )}

      {!stats && !isMonitoring && (
        <Text style={styles.placeholder}>
          Start monitoring to see system statistics
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  button: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 16,
  },
  startButton: {
    backgroundColor: '#4CAF50',
  },
  stopButton: {
    backgroundColor: '#f44336',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  statsContainer: {
    flex: 1,
  },
  section: {
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 12,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  stat: {
    fontSize: 14,
    marginBottom: 4,
    color: '#666',
  },
  historyItem: {
    marginBottom: 4,
  },
  historyText: {
    fontSize: 12,
    color: '#888',
  },
  placeholder: {
    textAlign: 'center',
    color: '#999',
    fontSize: 16,
    marginTop: 50,
  },
});

export default SystemMonitor;
