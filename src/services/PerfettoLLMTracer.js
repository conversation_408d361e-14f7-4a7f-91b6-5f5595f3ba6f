import { NativeModules } from 'react-native';

const { PerfettoLLMModule } = NativeModules;

class PerfettoLLMTracer {
  constructor() {
    this.isInitialized = false;
    this.nativeTracer = null;
    this.bindingsInstalled = false;
  }

  async initialize() {
    try {
      console.log('🔧 Initializing PerfettoLLMTracer...');

      // First, try to install JSI bindings manually
      if (PerfettoLLMModule && !this.bindingsInstalled) {
        try {
          await PerfettoLLMModule.installBindingsManually();
          this.bindingsInstalled = true;
          console.log('✅ JSI bindings installed successfully');
        } catch (error) {
          console.error('❌ Failed to install JSI bindings:', error);
          return false;
        }
      }

      // Check if JSI bindings are now available
      if (global.PerfettoLLMTracer) {
        this.nativeTracer = global.PerfettoLLMTracer;
        console.log('✅ PerfettoLLMTracer JSI bindings found');
      } else {
        console.warn('⚠️ PerfettoLLMTracer JSI bindings still not available after installation');
        return false;
      }

      // Initialize the native tracer
      const success = this.nativeTracer.initialize();
      this.isInitialized = success;
      
      if (success) {
        console.log('✅ Perfetto LLM Tracer initialized successfully');
      } else {
        console.error('❌ Failed to initialize Perfetto LLM Tracer');
      }
      
      return success;
    } catch (error) {
      console.error('❌ Error initializing Perfetto LLM Tracer:', error);
      return false;
    }
  }

  async startTrace(categories = ['llm', 'system', 'inference']) {
    try {
      if (!this.nativeTracer || !this.isInitialized) {
        console.warn('⚠️ Tracer not initialized');
        return false;
      }

      const categoriesStr = Array.isArray(categories) ? categories.join(',') : categories;
      const success = this.nativeTracer.startTrace(categoriesStr);
      
      if (success) {
        console.log(`✅ Perfetto trace started with categories: ${categoriesStr}`);
      } else {
        console.error('❌ Failed to start Perfetto trace');
      }
      
      return success;
    } catch (error) {
      console.error('❌ Error starting Perfetto trace:', error);
      return false;
    }
  }

  async stopTrace() {
    try {
      if (!this.nativeTracer) {
        console.warn('⚠️ Native tracer not available');
        return false;
      }

      const success = this.nativeTracer.stopTrace();
      
      if (success) {
        console.log('✅ Perfetto trace stopped successfully');
        console.log('📊 Check Android logs with: adb logcat | grep -E "(TRACE|PerfettoLLMTracer)"');
      } else {
        console.error('❌ Failed to stop Perfetto trace');
      }
      
      return success;
    } catch (error) {
      console.error('❌ Error stopping Perfetto trace:', error);
      return false;
    }
  }

  isTracing() {
    try {
      if (!this.nativeTracer) {
        return false;
      }
      return this.nativeTracer.isTracing();
    } catch (error) {
      console.error('❌ Error checking trace status:', error);
      return false;
    }
  }

  // LLM-specific instrumentation methods (same as before)
  traceInferenceStart(modelName, contextLength) {
    try {
      if (!this.nativeTracer || !this.isTracing()) {
        return;
      }
      this.nativeTracer.traceInferenceStart(modelName, contextLength);
      console.log(`📊 TRACE: Inference started - Model: ${modelName}, Context: ${contextLength}`);
    } catch (error) {
      console.warn('⚠️ Error tracing inference start:', error);
    }
  }

  traceInferenceEnd() {
    try {
      if (!this.nativeTracer || !this.isTracing()) {
        return;
      }
      this.nativeTracer.traceInferenceEnd();
      console.log('📊 TRACE: Inference ended');
    } catch (error) {
      console.warn('⚠️ Error tracing inference end:', error);
    }
  }

  tracePrefillStart(tokenCount) {
    try {
      if (!this.nativeTracer || !this.isTracing()) {
        return;
      }
      this.nativeTracer.tracePrefillStart(tokenCount);
      console.log(`📊 TRACE: Prefill started - Tokens: ${tokenCount}`);
    } catch (error) {
      console.warn('⚠️ Error tracing prefill start:', error);
    }
  }

  tracePrefillEnd() {
    try {
      if (!this.nativeTracer || !this.isTracing()) {
        return;
      }
      this.nativeTracer.tracePrefillEnd();
      console.log('📊 TRACE: Prefill ended');
    } catch (error) {
      console.warn('⚠️ Error tracing prefill end:', error);
    }
  }

  traceDecodeStart() {
    try {
      if (!this.nativeTracer || !this.isTracing()) {
        return;
      }
      this.nativeTracer.traceDecodeStart();
      console.log('📊 TRACE: Decode started');
    } catch (error) {
      console.warn('⚠️ Error tracing decode start:', error);
    }
  }

  traceDecodeEnd() {
    try {
      if (!this.nativeTracer || !this.isTracing()) {
        return;
      }
      this.nativeTracer.traceDecodeEnd();
      console.log('📊 TRACE: Decode ended');
    } catch (error) {
      console.warn('⚠️ Error tracing decode end:', error);
    }
  }

  traceTokenGenerated(token, tokenId) {
    try {
      if (!this.nativeTracer || !this.isTracing()) {
        return;
      }
      this.nativeTracer.traceTokenGenerated(token, tokenId);
      if (tokenId % 10 === 0) {
        console.log(`📊 TRACE: Token ${tokenId} generated`);
      }
    } catch (error) {
      console.warn('⚠️ Error tracing token generation:', error);
    }
  }

  // Enhanced monitoring methods
  getCurrentCpuUsage() {
    try {
      if (!this.nativeTracer) {
        return 0;
      }
      return this.nativeTracer.getCurrentCpuUsage();
    } catch (error) {
      console.warn('⚠️ Error getting current CPU usage:', error);
      return 0;
    }
  }

  getProcessCpuUsage() {
    try {
      if (!this.nativeTracer) {
        return 0;
      }
      return this.nativeTracer.getProcessCpuUsage();
    } catch (error) {
      console.warn('⚠️ Error getting process CPU usage:', error);
      return 0;
    }
  }

  getCurrentMemoryUsage() {
    try {
      if (!this.nativeTracer) {
        return 0;
      }
      return this.nativeTracer.getCurrentMemoryUsage();
    } catch (error) {
      console.warn('⚠️ Error getting current memory usage:', error);
      return 0;
    }
  }

  getProcessMemoryUsage() {
    try {
      if (!this.nativeTracer) {
        return 0;
      }
      return this.nativeTracer.getProcessMemoryUsage();
    } catch (error) {
      console.warn('⚠️ Error getting process memory usage:', error);
      return 0;
    }
  }

  startContinuousMonitoring() {
    try {
      if (!this.nativeTracer) {
        return;
      }
      this.nativeTracer.startContinuousMonitoring();
      console.log('📊 Started continuous monitoring');
    } catch (error) {
      console.warn('⚠️ Error starting continuous monitoring:', error);
    }
  }

  stopContinuousMonitoring() {
    try {
      if (!this.nativeTracer) {
        return;
      }
      this.nativeTracer.stopContinuousMonitoring();
      console.log('📊 Stopped continuous monitoring');
    } catch (error) {
      console.warn('⚠️ Error stopping continuous monitoring:', error);
    }
  }

  // Utility method to get comprehensive system stats
  getSystemStats() {
    return {
      systemCpu: this.getCurrentCpuUsage(),
      processCpu: this.getProcessCpuUsage(),
      systemMemory: this.getCurrentMemoryUsage(),
      processMemory: this.getProcessMemoryUsage(),
      timestamp: Date.now()
    };
  }

  // Get tracing status for debugging
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isTracing: this.isTracing(),
      nativeAvailable: !!this.nativeTracer,
      bindingsInstalled: this.bindingsInstalled,
      jsiBindingsAvailable: !!global.PerfettoLLMTracer,
      nativeModuleAvailable: !!PerfettoLLMModule
    };
  }
}

// Export singleton instance
export default new PerfettoLLMTracer();